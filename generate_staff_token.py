#!/usr/bin/env python
"""
Script สำหรับสร้าง JWT Token ของ Staff (TcdUsers) พร้อมกำหนดอายุได้

Usage:
    python generate_staff_token.py --user_id <user_id> --username <username> [--access_days <days>] [--refresh_days <days>]
    python generate_staff_token.py --list-staff
    python generate_staff_token.py --help

Examples:
    # สร้าง token สำหรับ staff user ID 1 และ username "admin" (อายุ default: access=1วัน, refresh=30วัน)
    python generate_staff_token.py --user_id 1 --username admin

    # สร้าง token พร้อมกำหนดอายุ access token = 7 วัน, refresh token = 60 วัน
    python generate_staff_token.py --user_id 1 --username admin --access_days 7 --refresh_days 60

    # แสดงรายชื่อ staff ทั้งหมด
    python generate_staff_token.py --list-staff
"""

import os
import sys
import django
import argparse
from datetime import datetime, timedelta
import logging

# Load environment variables from .env file
def load_env_file():
    """Load environment variables from .env file"""
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    # Handle export statements
                    if line.startswith('export '):
                        line = line[7:]  # Remove 'export '

                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value

# Load environment variables first
load_env_file()

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MCDC.settings')
django.setup()

from MCDC.models import TcdUsers
from authentication.services import StaffAuthService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CustomRefreshToken:
    """
    Custom RefreshToken class ที่สามารถกำหนดอายุได้
    """

    @staticmethod
    def create_token(access_days=1, refresh_days=30):
        """
        สร้าง custom refresh token ที่กำหนดอายุได้

        Args:
            access_days (int): จำนวนวันของ access token
            refresh_days (int): จำนวนวันของ refresh token

        Returns:
            RefreshToken: Custom refresh token
        """
        from authentication.views import NoBlacklistRefreshToken
        from datetime import timezone

        # สร้าง refresh token
        refresh = NoBlacklistRefreshToken()

        # กำหนดเวลาหมดอายุ
        now = datetime.now(timezone.utc)
        access_exp = now + timedelta(days=access_days)
        refresh_exp = now + timedelta(days=refresh_days)

        # แปลงเป็น timestamp
        access_exp_timestamp = int(access_exp.timestamp())
        refresh_exp_timestamp = int(refresh_exp.timestamp())

        # กำหนดเวลาหมดอายุใน payload
        refresh.payload['exp'] = refresh_exp_timestamp

        # สร้าง access token ด้วยอายุที่กำหนด
        access_token = refresh.access_token
        access_token.payload['exp'] = access_exp_timestamp

        return refresh, access_token


class StaffTokenGenerator:
    """
    Class สำหรับสร้าง JWT Token ของ Staff
    """

    @staticmethod
    def _create_simple_tokens(staff_user, access_days=1, refresh_days=30):
        """
        สร้าง JWT tokens แบบง่าย โดยไม่บันทึกลง blacklist database

        Args:
            staff_user: TcdUsers object
            access_days (int): จำนวนวันของ access token
            refresh_days (int): จำนวนวันของ refresh token

        Returns:
            dict: JWT tokens
        """
        try:
            # สร้าง custom token ที่กำหนดอายุได้
            refresh, access_token = CustomRefreshToken.create_token(access_days, refresh_days)

            # เพิ่มข้อมูล custom ใน token
            refresh['user_id'] = int(staff_user.id) if staff_user.id else None
            refresh['username'] = str(staff_user.username) if staff_user.username else None
            refresh['user_type'] = 'staff'
            refresh['email'] = str(staff_user.email) if staff_user.email else None
            refresh['first_name'] = str(staff_user.firstname) if staff_user.firstname else None
            refresh['last_name'] = str(staff_user.lastname) if staff_user.lastname else None

            # คัดลอกข้อมูลไปยัง access token ด้วย
            access_token['user_id'] = refresh['user_id']
            access_token['username'] = refresh['username']
            access_token['user_type'] = refresh['user_type']
            access_token['email'] = refresh['email']
            access_token['first_name'] = refresh['first_name']
            access_token['last_name'] = refresh['last_name']

            return {
                'refresh': str(refresh),
                'access': str(access_token),
            }
        except Exception as e:
            logger.error(f"Error creating simple tokens: {str(e)}")
            return None
    
    @staticmethod
    def list_staff_users():
        """
        แสดงรายชื่อ Staff Users ทั้งหมด
        """
        try:
            staff_users = TcdUsers.objects.all().order_by('id')
            
            if not staff_users.exists():
                print("ไม่พบ Staff Users ในระบบ")
                return
            
            print("\n=== รายชื่อ Staff Users ===")
            print(f"{'ID':<5} {'Username':<20} {'ชื่อ-นามสกุล':<30} {'Email':<30} {'Position':<20}")
            print("-" * 105)
            
            for staff in staff_users:
                full_name = f"{staff.firstname} {staff.lastname}"
                print(f"{staff.id:<5} {staff.username:<20} {full_name:<30} {staff.email:<30} {staff.position:<20}")
            
            print(f"\nรวมทั้งหมด: {staff_users.count()} คน")
            
        except Exception as e:
            logger.error(f"Error listing staff users: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการดึงข้อมูล Staff Users: {str(e)}")
    
    @staticmethod
    def generate_staff_token(user_id, username, access_days=1, refresh_days=30):
        """
        สร้าง JWT Token สำหรับ Staff User

        Args:
            user_id (int): ID ของ Staff User
            username (str): Username ของ Staff User
            access_days (int): จำนวนวันของ access token (default: 1)
            refresh_days (int): จำนวนวันของ refresh token (default: 30)

        Returns:
            dict: JWT tokens หรือ None ถ้าเกิดข้อผิดพลาด
        """
        try:
            # ค้นหา Staff User
            staff_user = TcdUsers.objects.get(id=user_id, username=username)

            print(f"\n=== ข้อมูล Staff User ===")
            print(f"ID: {staff_user.id}")
            print(f"Username: {staff_user.username}")
            print(f"ชื่อ-นามสกุล: {staff_user.firstname} {staff_user.lastname}")
            print(f"Email: {staff_user.email}")
            print(f"Position: {staff_user.position}")

            # สร้าง JWT Tokens โดยไม่บันทึกลง blacklist database
            try:
                # ใช้ default service ก่อน แต่ถ้า error ให้ใช้ custom token
                tokens = StaffAuthService.generate_tokens(staff_user)
            except Exception as token_error:
                # หากเกิด error ในการบันทึก blacklist ให้สร้าง token แบบง่าย
                logger.warning(f"Blacklist database error, creating simple tokens: {str(token_error)}")
                tokens = None

            # ถ้าต้องการกำหนดอายุเอง หรือ service ไม่ทำงาน ให้ใช้ custom token
            if access_days != 1 or refresh_days != 30 or not tokens:
                logger.info(f"Creating custom tokens with access_days={access_days}, refresh_days={refresh_days}")
                tokens = StaffTokenGenerator._create_simple_tokens(staff_user, access_days, refresh_days)

            if tokens:
                print(f"\n=== JWT Tokens (อายุ Access: {access_days} วัน, Refresh: {refresh_days} วัน) ===")
                print(f"Access Token:")
                print(f"{tokens['access']}")
                print(f"\nRefresh Token:")
                print(f"{tokens['refresh']}")

                # แสดงข้อมูลเพิ่มเติมเกี่ยวกับ token
                try:
                    # Import NoBlacklistRefreshToken to avoid database blacklist operations
                    from authentication.views import NoBlacklistRefreshToken
                    from rest_framework_simplejwt.tokens import AccessToken

                    refresh_token = NoBlacklistRefreshToken(tokens['refresh'])
                    access_token = AccessToken(tokens['access'])

                    print(f"\n=== Token Information ===")
                    print(f"Token Type: staff")
                    print(f"User ID: {refresh_token.payload.get('user_id')}")
                    print(f"Username: {refresh_token.payload.get('username')}")
                    print(f"User Type: {refresh_token.payload.get('user_type')}")
                    print(f"Email: {refresh_token.payload.get('email')}")
                    print(f"First Name: {refresh_token.payload.get('first_name')}")
                    print(f"Last Name: {refresh_token.payload.get('last_name')}")

                    # แสดงวันหมดอายุ
                    access_exp = access_token.payload.get('exp')
                    refresh_exp = refresh_token.payload.get('exp')

                    if access_exp:
                        from datetime import timezone as dt_timezone
                        access_expire_date = datetime.fromtimestamp(access_exp, tz=dt_timezone.utc)
                        print(f"Access Token หมดอายุ: {access_expire_date.strftime('%Y-%m-%d %H:%M:%S UTC')} ({access_days} วัน)")

                    if refresh_exp:
                        from datetime import timezone as dt_timezone
                        refresh_expire_date = datetime.fromtimestamp(refresh_exp, tz=dt_timezone.utc)
                        print(f"Refresh Token หมดอายุ: {refresh_expire_date.strftime('%Y-%m-%d %H:%M:%S UTC')} ({refresh_days} วัน)")

                except Exception as parse_error:
                    logger.warning(f"Could not parse token details: {str(parse_error)}")

                print(f"\n=== การใช้งาน ===")
                print(f"ใส่ Access Token ใน Header:")
                print(f"Authorization: Bearer {tokens['access']}")
                print(f"\n✅ Token สร้างสำเร็จ!")

                return tokens
            else:
                print("เกิดข้อผิดพลาดในการสร้าง Token")
                return None

        except TcdUsers.DoesNotExist:
            logger.error(f"Staff user not found: id={user_id}, username={username}")
            print(f"ไม่พบ Staff User ที่มี ID: {user_id} และ Username: {username}")
            return None
        except Exception as e:
            logger.error(f"Error generating staff token: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการสร้าง Token: {str(e)}")
            return None
    
    @staticmethod
    def generate_token_by_username_only(username, access_days=1, refresh_days=30):
        """
        สร้าง JWT Token สำหรับ Staff User โดยใช้ username เท่านั้น

        Args:
            username (str): Username ของ Staff User
            access_days (int): จำนวนวันของ access token (default: 1)
            refresh_days (int): จำนวนวันของ refresh token (default: 30)

        Returns:
            dict: JWT tokens หรือ None ถ้าเกิดข้อผิดพลาด
        """
        try:
            # ค้นหา Staff User โดยใช้ username เท่านั้น
            staff_user = TcdUsers.objects.get(username=username)

            return StaffTokenGenerator.generate_staff_token(staff_user.id, staff_user.username, access_days, refresh_days)

        except TcdUsers.DoesNotExist:
            logger.error(f"Staff user not found with username: {username}")
            print(f"ไม่พบ Staff User ที่มี Username: {username}")
            return None
        except Exception as e:
            logger.error(f"Error generating staff token: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการสร้าง Token: {str(e)}")
            return None


def main():
    """
    Main function สำหรับรัน script
    """
    parser = argparse.ArgumentParser(
        description='สร้าง JWT Token สำหรับ Staff (TcdUsers) พร้อมกำหนดอายุได้',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --user_id 1 --username admin
  %(prog)s --username admin --access_days 7 --refresh_days 60
  %(prog)s --list-staff
        """
    )

    parser.add_argument('--user_id', type=int, help='ID ของ Staff User')
    parser.add_argument('--username', type=str, help='Username ของ Staff User')
    parser.add_argument('--access_days', type=int, default=1, help='จำนวนวันของ Access Token (default: 1)')
    parser.add_argument('--refresh_days', type=int, default=30, help='จำนวนวันของ Refresh Token (default: 30)')
    parser.add_argument('--list-staff', action='store_true', help='แสดงรายชื่อ Staff Users ทั้งหมด')
    
    args = parser.parse_args()
    
    # ตรวจสอบ arguments
    if args.list_staff:
        StaffTokenGenerator.list_staff_users()
    elif args.username:
        # ตรวจสอบค่า access_days และ refresh_days
        if args.access_days < 1:
            print("Error: access_days ต้องมากกว่า 0")
            sys.exit(1)
        if args.refresh_days < 1:
            print("Error: refresh_days ต้องมากกว่า 0")
            sys.exit(1)

        if args.user_id:
            # ใช้ทั้ง user_id และ username
            StaffTokenGenerator.generate_staff_token(args.user_id, args.username, args.access_days, args.refresh_days)
        else:
            # ใช้ username เท่านั้น
            StaffTokenGenerator.generate_token_by_username_only(args.username, args.access_days, args.refresh_days)
    else:
        parser.print_help()
        print("\nกรุณาระบุ --username หรือ --list-staff")
        sys.exit(1)


if __name__ == '__main__':
    main()
