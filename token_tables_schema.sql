-- =====================================================
-- MCDC Token Management Tables Schema
-- =====================================================
-- This script creates the token management tables for JWT authentication
-- Used by Django REST framework SimpleJWT with custom table names
-- =====================================================

-- =====================================================
-- Table: tcd_outstanding_tokens
-- Purpose: Stores all issued JWT tokens (both access and refresh tokens)
-- =====================================================
CREATE TABLE [dbo].[tcd_outstanding_tokens] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [jti] UNIQUEIDENTIFIER NOT NULL,                    -- JWT ID (unique identifier for each token)
    [token] NVARCHAR(500) COLLATE Thai_CI_AI NOT NULL, -- Token string (truncated to 500 chars)
    [created_at] DATETIME2(7) NOT NULL,                -- Token creation timestamp
    [expires_at] DATETIME2(7) NOT NULL,                -- Token expiration timestamp
    [user_id] INT NOT NULL,                            -- User ID (references tcd_app_member.id or tcd_user_consult.id)
    [user_type] NVARCHAR(20) COLLATE Thai_CI_AI NOT NULL, -- User type: 'member', 'consultant', 'staff'
    CONSTRAINT [PK_tcd_outstanding_tokens] PRIMARY KEY CLUSTERED ([id] ASC)
);

-- =====================================================
-- Table: tcd_blacklisted_tokens
-- Purpose: Stores blacklisted tokens (revoked/invalidated tokens)
-- =====================================================
CREATE TABLE [dbo].[tcd_blacklisted_tokens] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [token_id] INT NOT NULL,                           -- Foreign key to tcd_outstanding_tokens.id
    [blacklisted_at] DATETIME2(7) NOT NULL,           -- When the token was blacklisted
    CONSTRAINT [PK_tcd_blacklisted_tokens] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_tcd_blacklisted_tokens_token_id] FOREIGN KEY ([token_id]) 
        REFERENCES [dbo].[tcd_outstanding_tokens] ([id]) ON DELETE CASCADE
);

-- =====================================================
-- Indexes for tcd_outstanding_tokens
-- =====================================================

-- Index for JTI lookups (most common query pattern)
CREATE UNIQUE NONCLUSTERED INDEX [IX_tcd_outstanding_tokens_jti] 
ON [dbo].[tcd_outstanding_tokens] ([jti] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- Index for user-based queries (finding all tokens for a specific user)
CREATE NONCLUSTERED INDEX [IX_tcd_outstanding_tokens_user_id_user_type] 
ON [dbo].[tcd_outstanding_tokens] ([user_id] ASC, [user_type] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- Index for expiration-based cleanup queries
CREATE NONCLUSTERED INDEX [IX_tcd_outstanding_tokens_expires_at] 
ON [dbo].[tcd_outstanding_tokens] ([expires_at] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- Composite index for user and expiration queries
CREATE NONCLUSTERED INDEX [IX_tcd_outstanding_tokens_user_expires] 
ON [dbo].[tcd_outstanding_tokens] ([user_id] ASC, [user_type] ASC, [expires_at] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- =====================================================
-- Indexes for tcd_blacklisted_tokens
-- =====================================================

-- Index for foreign key lookups
CREATE NONCLUSTERED INDEX [IX_tcd_blacklisted_tokens_token_id] 
ON [dbo].[tcd_blacklisted_tokens] ([token_id] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- Index for blacklist timestamp queries
CREATE NONCLUSTERED INDEX [IX_tcd_blacklisted_tokens_blacklisted_at] 
ON [dbo].[tcd_blacklisted_tokens] ([blacklisted_at] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- =====================================================
-- Additional Performance Optimizations
-- =====================================================

-- Include columns for better query performance
-- This index includes commonly accessed columns to avoid key lookups
CREATE NONCLUSTERED INDEX [IX_tcd_outstanding_tokens_jti_include] 
ON [dbo].[tcd_outstanding_tokens] ([jti] ASC)
INCLUDE ([user_id], [user_type], [expires_at])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
      DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);

-- =====================================================
-- Comments and Documentation
-- =====================================================

-- Add table comments
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Stores all issued JWT tokens (both access and refresh tokens) for tracking and blacklist management', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Stores blacklisted tokens (revoked/invalidated tokens) for security management', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'tcd_blacklisted_tokens';

-- Add column comments for tcd_outstanding_tokens
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Primary key', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'JWT ID - unique identifier for each token', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'jti';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Token string (truncated to 500 characters)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'token';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Token creation timestamp', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'created_at';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Token expiration timestamp', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'expires_at';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'User ID (references tcd_app_member.id or tcd_user_consult.id)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'user_id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'User type: member, consultant, or staff', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_outstanding_tokens', @level2type = N'COLUMN', @level2name = N'user_type';

-- Add column comments for tcd_blacklisted_tokens
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Primary key', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_blacklisted_tokens', @level2type = N'COLUMN', @level2name = N'id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Foreign key to tcd_outstanding_tokens.id', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_blacklisted_tokens', @level2type = N'COLUMN', @level2name = N'token_id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'When the token was blacklisted', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'tcd_blacklisted_tokens', @level2type = N'COLUMN', @level2name = N'blacklisted_at';

-- =====================================================
-- Maintenance Scripts
-- =====================================================

-- Create a stored procedure for cleaning up expired tokens
-- CREATE PROCEDURE [dbo].[sp_CleanupExpiredTokens]
--     @DaysToKeep INT = 30
-- AS
-- BEGIN
--     SET NOCOUNT ON;
    
--     DECLARE @CutoffDate DATETIME2(7) = DATEADD(DAY, -@DaysToKeep, GETUTCDATE());
    
--     BEGIN TRANSACTION;
    
--     BEGIN TRY
--         -- Delete blacklisted tokens older than cutoff date
--         DELETE bt 
--         FROM [dbo].[tcd_blacklisted_tokens] bt
--         INNER JOIN [dbo].[tcd_outstanding_tokens] ot ON bt.token_id = ot.id
--         WHERE ot.expires_at < @CutoffDate;
        
--         -- Delete expired outstanding tokens
--         DELETE FROM [dbo].[tcd_outstanding_tokens] 
--         WHERE expires_at < @CutoffDate;
        
--         COMMIT TRANSACTION;
        
--         PRINT 'Cleanup completed successfully.';
--     END TRY
--     BEGIN CATCH
--         ROLLBACK TRANSACTION;
--         PRINT 'Error during cleanup: ' + ERROR_MESSAGE();
--         THROW;
--     END CATCH
-- END;

-- =====================================================
-- Sample Queries for Common Operations
-- =====================================================

/*
-- Check if a token is blacklisted
SELECT COUNT(*) 
FROM tcd_blacklisted_tokens bt
INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
WHERE ot.jti = CONVERT(UNIQUEIDENTIFIER, 'your-jti-here');

-- Find all active tokens for a user
SELECT * 
FROM tcd_outstanding_tokens 
WHERE user_id = 123 
  AND user_type = 'member' 
  AND expires_at > GETUTCDATE();

-- Find all blacklisted tokens for a user
SELECT ot.*, bt.blacklisted_at
FROM tcd_outstanding_tokens ot
INNER JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
WHERE ot.user_id = 123 
  AND ot.user_type = 'member';

-- Clean up expired tokens (run periodically)
EXEC sp_CleanupExpiredTokens @DaysToKeep = 30;
*/

-- =====================================================
-- End of Script
-- ===================================================== 