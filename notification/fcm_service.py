import firebase_admin
from firebase_admin import credentials
from firebase_admin import messaging
import os
import logging

logger = logging.getLogger(__name__)

def initialize_firebase():
    try:
        # cred = credentials.Certificate('keys/serviceAccountKey.json')
        cred = credentials.Certificate('keys/cdc.json')
        firebase_admin.initialize_app(cred)
    except Exception as e:
        logger.error(f"Error initializing Firebase: {e}")

def send_push_notification(device_token, title, body, data=None):
    """
    ส่ง Push Notification ไปยังอุปกรณ์เป้าหมาย

    Args:
        device_token (str): FCM registration token ของอุปกรณ์เป้าหมาย
        title (str): หัวข้อของการแจ้งเตือน
        body (str): เนื้อหาของการแจ้งเตือน
        data (dict, optional): ข้อมูลเพิ่มเติมที่จะส่งไปพร้อมกับการแจ้งเตือน
                                (จะถูกรับใน data payload ของแอป)
    """
    try:
        initialize_firebase()
        # สร้าง Message object
        message = messaging.Message(
            notification=messaging.Notification(
                title=title,
                body=body,
            ),
            data=data,
            token=device_token,
        )

        # ส่งข้อความ
        response = messaging.send(message)
        logger.info(f"Successfully sent message: {response}")

    except Exception as e:
        logger.error(f"Error sending message: {e}")


# if __name__ == '__main__':
#     token = ''
#     news_id = '1'
#     data = {
#         'type': 'news',
#         'ref_id': news_id,
#         'route': f'/news/{news_id}',
#     }
#     send_push_notification(token, 'ประกาศข่าวสาร', 'ข้อความประกาศข่าวสารที่ส่งมาจากระบบ', data)