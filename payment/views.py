from django.shortcuts import render
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from drf_spectacular.utils import extend_schema, OpenApiParameter
import logging

from utils.response import APIResponse, get_language_from_request
from .serializers import PaymentDetailListResponseSerializer, PaymentDetailByIdResponseSerializer
from .services import PaymentDetailService

logger = logging.getLogger(__name__)


@extend_schema(
    summary="Get payment detail list",
    description="ดึงรายการรายละเอียดการชำระเงินของที่ปรึกษาที่ล็อกอินอยู่ - แสดง 10 รายการล่าสุด เรียงลำดับจากวันที่เรียกเก็บมากไปน้อย",
    parameters=[
        OpenApiParameter(
            name='page',
            description='หน้าที่ต้องการ (default: 1)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
        OpenApiParameter(
            name='page_size',
            description='จำนวนรายการต่อหน้า (default: 10)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
    ],
    responses={
        200: PaymentDetailListResponseSerializer,
        400: "Bad Request - Invalid parameters",
        401: "Unauthorized - Authentication required",
        404: "Not Found - Data not found",
        500: "Internal Server Error"
    },
    tags=["Payment"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_detail_list(request):
    """
    Get payment detail list for authenticated consultant
    
    ดึงรายการรายละเอียดการชำระเงินของที่ปรึกษาที่ล็อกอินอยู่:
    - เลขที่ใบแจ้งการชำระ = payment.bill_no
    - รหัสอ้างอิง (Ref.) 1 = payment.cgd_ref1
    - วันที่เรียกเก็บ = payment.create_date (d MMM yyyy HH:mm format)
    - วันที่ครบกำหนด = payment.expire_date (d MMM yyyy HH:mm format)
    - สถานะ = ชื่อ payment.status (0: รอชำระ, 1: รอตรวจสอบ, 2: ยืนยันการชำระ, 3: ไม่อนุมัติ)
    - รายละเอียด = ตาม request_type_id (1: ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา, 2: ค่าธรรมเนียมการต่อทะเบียนที่ปรึกษา)
    - วันที่ดำเนินการ = payment.pay_date หรือ "-" ถ้าเป็น null
    - ยอดชำระทั้งหมด = payment.amount (money format ทศนิยม 2 ตำแหน่ง)
    - การแสดงปุ่ม: status=0 แสดงทั้งสองปุ่ม, status=1 แสดงปุ่มหลักฐานการชำระ, status=2,3 ไม่แสดงปุ่ม
    - แสดง 10 รายการล่าสุด เรียงลำดับจาก payment.create_date มากไปน้อย
    - แสดงผลตามภาษาที่ผู้ใช้งานตั้งค่าไว้ (TH|EN)
    """
    try:
        # Get language from request
        language = get_language_from_request(request)
        
        # Check if user is authenticated and is a consultant
        if not hasattr(request.user, 'user_type') or request.user.user_type != 'consultant':
            return APIResponse.error(
                error_code=1006,  # Access denied
                language=language,
                status_code=status.HTTP_403_FORBIDDEN
            )
        
        # Get user_consult_id from authenticated user (JWT token)
        user_consult_id = request.user.id
        
        # Get query parameters for pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 10)
        
        # Convert and validate pagination parameters
        try:
            page = int(page)
            page_size = int(page_size)
            
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:  # Limit page_size
                page_size = 10
        
        except (ValueError, TypeError):
            page = 1
            page_size = 10
        
        # Call service to get payment details
        service_response = PaymentDetailService.get_payment_detail_list(
            user_consult_id=user_consult_id,
            page=page,
            page_size=page_size,
            language=language
        )
        
        # Check service response
        if not service_response.get('success'):
            return APIResponse.error(
                error_code=service_response.get('error_code', 5000),
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Return success response with pagination format
        data = service_response.get('data', {})
        
        # Create response using Django JsonResponse directly to match user rules format
        from django.http import JsonResponse
        
        response_data = {
            "success": True,
            "error_code": None,
            "error_message": None,
            "data": data.get('payments', []),
            "page": data.get('page', 1),
            "per_page": data.get('per_page', 10),
            "total": data.get('total', 0),
            "has_next": data.get('has_next', False),
            "api_version": "v.0.0.1"
        }
        
        return JsonResponse(response_data, status=status.HTTP_200_OK, json_dumps_params={'ensure_ascii': False})
        
    except Exception as e:
        logger.error(f"Error in payment_detail_list: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get payment detail by ID",
    description="ดึงรายละเอียดการชำระเงินตาม ID สำหรับที่ปรึกษาที่ล็อกอินอยู่",
    responses={
        200: PaymentDetailByIdResponseSerializer,
        400: "Bad Request - Invalid parameters",
        401: "Unauthorized - Authentication required",
        403: "Forbidden - Access denied",
        404: "Not Found - Payment not found",
        500: "Internal Server Error"
    },
    tags=["Payment"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_detail_by_id(request, payment_id):
    """
    Get payment detail by ID for authenticated consultant
    
    ดึงรายละเอียดการชำระเงินตาม ID สำหรับที่ปรึกษาที่ล็อกอินอยู่:
    - ตรวจสอบสิทธิ์การเข้าถึง (เฉพาะที่ปรึกษาเจ้าของข้อมูล)
    - ส่งคืนข้อมูลการชำระเงินทั้งหมด
    - แสดงผลตามภาษาที่ผู้ใช้งานตั้งค่าไว้ (TH|EN)
    """
    try:
        # Get language from request
        language = get_language_from_request(request)
        
        # Check if user is authenticated and is a consultant
        if not hasattr(request.user, 'user_type') or request.user.user_type != 'consultant':
            return APIResponse.error(
                error_code=1006,  # Access denied
                language=language,
                status_code=status.HTTP_403_FORBIDDEN
            )
        
        # Get user_consult_id from authenticated user (JWT token)
        user_consult_id = request.user.id
        
        # Validate payment_id
        try:
            payment_id = int(payment_id)
            if payment_id < 1:
                return APIResponse.error(
                    error_code=1003,  # Invalid parameter
                    language=language,
                    status_code=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return APIResponse.error(
                error_code=1003,  # Invalid parameter
                language=language,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Call service to get payment detail by ID
        service_response = PaymentDetailService.get_payment_detail_by_id(
            payment_id=payment_id,
            user_consult_id=user_consult_id,
            language=language
        )
        
        # Check service response
        if not service_response.get('success'):
            error_code = service_response.get('error_code', 5000)
            if error_code == 5000:  # Not found
                return APIResponse.error(
                    error_code=error_code,
                    language=language,
                    status_code=status.HTTP_404_NOT_FOUND
                )
            else:
                return APIResponse.error(
                    error_code=error_code,
                    language=language,
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        # Return success response
        data = service_response.get('data', {})
        
        # Create response using Django JsonResponse directly to match user rules format
        from django.http import JsonResponse
        
        response_data = {
            "success": True,
            "error_code": None,
            "error_message": None,
            "data": data,
            "api_version": "v.0.0.1"
        }
        
        return JsonResponse(response_data, status=status.HTTP_200_OK, json_dumps_params={'ensure_ascii': False})
        
    except Exception as e:
        logger.error(f"Error in payment_detail_by_id: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
