"""
Constants สำหรับ Authentication App
"""

# สถานะสมาชิก (tcd_app_member.status)
class MemberStatus:
    INACTIVE = '0'  # ไม่ใช้งาน
    ACTIVE = '1'    # ใช้งาน  
    DELETED = 'D'   # ลบบัญชี
    
    CHOICES = [
        (INACTIVE, 'ไม่ใช้งาน'),
        (ACTIVE, 'ใช้งาน'),
        (DELETED, 'ลบบัญชี'),
    ]
    
    @classmethod
    def is_active(cls, status):
        """ตรวจสอบว่าสถานะเป็น active หรือไม่"""
        return status == cls.ACTIVE


# สถานะการยืนยัน (tcd_user_consult.verify)
class ConsultantVerificationStatus:
    UNVERIFIED = '0'  # ยังไม่ยืนยัน
    VERIFIED = '1'    # ยืนยันแล้ว
    
    CHOICES = [
        (UNVERIFIED, 'ยังไม่ยืนยัน'),
        (VERIFIED, 'ยืนยันแล้ว'),
    ]
    
    @classmethod
    def is_verified(cls, verify_status):
        """ตรวจสอบว่าได้รับการยืนยันแล้วหรือไม่"""
        return verify_status == cls.VERIFIED


# สถานะการแจ้งเตือน (ใช้ร่วมกัน)
class NotificationStatus:
    DISABLED = '0'  # ปิดการแจ้งเตือน
    ENABLED = '1'   # เปิดการแจ้งเตือน
    
    CHOICES = [
        (DISABLED, 'ปิดการแจ้งเตือน'),
        (ENABLED, 'เปิดการแจ้งเตือน'),
    ]


# การตรวจสอบการเข้าใช้งาน (tcd_app_member.checklogin)
class LoginAttempts:
    MAX_ATTEMPTS = 3      # จำนวนครั้งสูงสุดที่อนุญาต
    LOCKOUT_SECONDS = 30  # ระยะเวลาล็อค (วินาที)
    
    @classmethod
    def is_locked_out(cls, attempts):
        """ตรวจสอบว่าควรล็อคบัญชีหรือไม่"""
        return attempts >= cls.MAX_ATTEMPTS


# สถานะระบบจับคู่ (tcd_user_consult.is_active_matching)
class MatchingStatus:
    INACTIVE = False  # ไม่เข้าใช้งานระบบจับคู่
    ACTIVE = True     # เข้าใช้งานระบบจับคู่
    
    @classmethod
    def is_active_matching(cls, status):
        """ตรวจสอบว่าเปิดใช้งานระบบจับคู่หรือไม่"""
        return status == cls.ACTIVE


# ภาษา (ใช้ร่วมกัน)
class Language:
    THAI = 'th'
    ENGLISH = 'en'
    
    CHOICES = [
        (THAI, 'ไทย'),
        (ENGLISH, 'English'),
    ]
    
    DEFAULT = THAI


# ข้อความ Error
class ErrorMessages:
    INVALID_CREDENTIALS = 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
    ACCOUNT_INACTIVE = 'บัญชีผู้ใช้ไม่ถูกต้องหรือถูกปิดใช้งาน'
    ACCOUNT_DELETED = 'บัญชีผู้ใช้ถูกลบแล้ว'
    ACCOUNT_LOCKED = 'บัญชีถูกล็อค กรุณาลองใหม่ในภายหลัง'
    ACCOUNT_UNVERIFIED = 'บัญชีผู้ใช้ยังไม่ได้รับการยืนยัน'
    ACCOUNT_VERIFICATION_PENDING = 'บัญชีผู้ใช้อยู่ระหว่างการตรวจสอบ'
    ACCOUNT_VERIFICATION_REJECTED = 'บัญชีผู้ใช้ถูกปฏิเสธการยืนยัน'
    MISSING_CREDENTIALS = 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน'
    
    @classmethod
    def get_status_message(cls, status):
        """ดึงข้อความ error ตาม member status"""
        if status == MemberStatus.INACTIVE:
            return cls.ACCOUNT_INACTIVE
        elif status == MemberStatus.DELETED:
            return cls.ACCOUNT_DELETED
        else:
            return cls.ACCOUNT_INACTIVE
    
    @classmethod
    def get_verification_message(cls, verify_status):
        """ดึงข้อความ error ตาม consultant verification status"""
        if verify_status == ConsultantVerificationStatus.UNVERIFIED:
            return cls.ACCOUNT_UNVERIFIED
        else:
            return cls.ACCOUNT_UNVERIFIED


# ประเภทที่ปรึกษา (tcd_user_consult.consult_type)
class ConsultantType:
    """
    ประเภทที่ปรึกษา - ค่าเหล่านี้อาจต้องปรับตาม master data ที่แท้จริง
    """
    INDEPENDENT = 1     # ที่ปรึกษาอิสระ
    CORPORATE = 2   # ที่ปรึกษานิติบุคคล
    
    CHOICES = [
        (INDEPENDENT, 'ที่ปรึกษาอิสระ'),
        (CORPORATE, 'ที่ปรึกษานิติบุคคล'),
    ]


# API Connection และ Error Constants
class APIErrorCodes:
    # Connection Errors
    CONNECTION_FAILED = 'connection_failed'
    CONNECTION_TIMEOUT = 'connection_timeout'
    
    # API Key Errors
    INVALID_API_KEY = 'invalid_api_key'
    API_KEY_EXPIRED = 'api_key_expired'
    API_KEY_MISSING = 'api_key_missing'
    
    # Authentication Errors
    AUTHENTICATION_FAILED = 'authentication_failed'
    
    # Data Processing Errors
    LOGIN_DATA_SAVE_FAILED = 'login_data_save_failed'
    USER_DATA_SEND_FAILED = 'user_data_send_failed'
    DATA_TRANSMISSION_FAILED = 'data_transmission_failed'
    USER_DISPLAY_FAILED = 'user_display_failed'
    LOGIN_RECORD_SAVE_FAILED = 'login_record_save_failed'


class APIErrorMessages:
    # Connection Messages
    CONNECTION_FAILED = 'ไม่สามารถเชื่อมต่อกับระบบได้ กรุณาลองใหม่อีกครั้ง'
    CONNECTION_TIMEOUT = 'การเชื่อมต่อหมดเวลา กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ต'
    
    # API Key Messages
    INVALID_API_KEY = 'API Key ไม่ถูกต้อง กรุณาติดต่อผู้ดูแลระบบ'
    API_KEY_EXPIRED = 'API Key หมดอายุ กรุณาติดต่อผู้ดูแลระบบ'
    API_KEY_MISSING = 'ไม่พบ API Key กรุณาติดต่อผู้ดูแลระบบ'
    
    # Authentication Messages
    AUTHENTICATION_FAILED = 'การยืนยันตัวตนล้มเหลว กรุณาตรวจสอบข้อมูลการเข้าสู่ระบบ'
    
    # Data Processing Messages
    LOGIN_DATA_SAVE_FAILED = 'ไม่สามารถบันทึกข้อมูลการเข้าสู่ระบบได้'
    USER_DATA_SEND_FAILED = 'ไม่สามารถส่งข้อมูลผู้ใช้งานได้'
    DATA_TRANSMISSION_FAILED = 'การส่งข้อมูลล้มเหลว กรุณาลองใหม่อีกครั้ง'
    USER_DISPLAY_FAILED = 'ไม่สามารถแสดงข้อมูลผู้ใช้งานได้'
    LOGIN_RECORD_SAVE_FAILED = 'ไม่สามารถบันทึกประวัติการเข้าสู่ระบบได้'
    
    @classmethod
    def get_api_error_message(cls, error_code):
        """ดึงข้อความ error ตาม API error code"""
        error_messages = {
            APIErrorCodes.CONNECTION_FAILED: cls.CONNECTION_FAILED,
            APIErrorCodes.CONNECTION_TIMEOUT: cls.CONNECTION_TIMEOUT,
            APIErrorCodes.INVALID_API_KEY: cls.INVALID_API_KEY,
            APIErrorCodes.API_KEY_EXPIRED: cls.API_KEY_EXPIRED,
            APIErrorCodes.API_KEY_MISSING: cls.API_KEY_MISSING,
            APIErrorCodes.AUTHENTICATION_FAILED: cls.AUTHENTICATION_FAILED,
            APIErrorCodes.LOGIN_DATA_SAVE_FAILED: cls.LOGIN_DATA_SAVE_FAILED,
            APIErrorCodes.USER_DATA_SEND_FAILED: cls.USER_DATA_SEND_FAILED,
            APIErrorCodes.DATA_TRANSMISSION_FAILED: cls.DATA_TRANSMISSION_FAILED,
            APIErrorCodes.USER_DISPLAY_FAILED: cls.USER_DISPLAY_FAILED,
            APIErrorCodes.LOGIN_RECORD_SAVE_FAILED: cls.LOGIN_RECORD_SAVE_FAILED,
        }
        return error_messages.get(error_code, 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ')


# API Configuration
class APIConfig:
    """การกำหนดค่า API"""
    DEFAULT_TIMEOUT = 30  # seconds
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1  # seconds
    
    # API Endpoints (สามารถปรับตาม environment ได้)
    AUTH_ENDPOINT = '/api/auth'
    USER_DATA_ENDPOINT = '/api/user-data'
    LOGIN_RECORD_ENDPOINT = '/api/login-record'


# Login Session Management
class LoginSessionStatus:
    """สถานะของ session การ login"""
    INITIATED = 'initiated'
    VALIDATING = 'validating'
    AUTHENTICATED = 'authenticated'
    DATA_PROCESSING = 'data_processing'
    COMPLETED = 'completed'
    FAILED = 'failed'
    
    CHOICES = [
        (INITIATED, 'เริ่มต้นการเข้าสู่ระบบ'),
        (VALIDATING, 'กำลังตรวจสอบข้อมูล'),
        (AUTHENTICATED, 'ยืนยันตัวตนสำเร็จ'),
        (DATA_PROCESSING, 'กำลังประมวลผลข้อมูล'),
        (COMPLETED, 'เข้าสู่ระบบสำเร็จ'),
        (FAILED, 'เข้าสู่ระบบล้มเหลว'),
    ]


class OTPStatus:
    """สถานะของ OTP"""
    PENDING = 'pending'
    VERIFIED = 'verified'
    EXPIRED = 'expired'
    
    CHOICES = [
        (PENDING, 'รอการยืนยัน'),
        (VERIFIED, 'ยืนยันแล้ว'),
        (EXPIRED, 'หมดอายุ'),
    ]


class OTPPurpose:
    """วัตถุประสงค์ของ OTP"""
    LOGIN = 'login'
    REGISTER = 'register'
    RESET_PASSWORD = 'reset_password'
    VERIFY_EMAIL = 'verify_email'
    VERIFY_PHONE = 'verify_phone'
    
    CHOICES = [
        (LOGIN, 'เข้าสู่ระบบ'),
        (REGISTER, 'ลงทะเบียน'),
        (RESET_PASSWORD, 'รีเซ็ตรหัสผ่าน'),
        (VERIFY_EMAIL, 'ยืนยันอีเมล'),
        (VERIFY_PHONE, 'ยืนยันเบอร์โทรศัพท์'),
    ]


class OTPErrorCodes:
    """รหัสข้อผิดพลาดของ OTP"""
    OTP_ALREADY_VERIFIED = 2020
    OTP_EXPIRED = 2021
    OTP_MAX_ATTEMPTS_EXCEEDED = 2022
    OTP_INVALID = 2023
    OTP_TOKEN_EXPIRED = 2024
    OTP_TOKEN_INVALID = 2025
    OTP_TOKEN_NOT_VERIFIED = 2026
    OTP_GENERATION_FAILED = 2027
    OTP_VERIFICATION_FAILED = 2028 