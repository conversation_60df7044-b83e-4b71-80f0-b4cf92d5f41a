from django.db import models
from authentication.models import TcdAppMember, TcdAppMasGovernmentSector, TcdAppMasMinistry, TcdAppMasDepartment


class TcdSector(models.Model):
    code = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_sector'


class TcdSkill(models.Model):
    sector_id = models.IntegerField(blank=True, null=True)
    code = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    st = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    nd = models.IntegerField(blank=True, null=True)
    rd = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_skill'


class TcdService(models.Model):
    code = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_service'





class TcdAppNotification(models.Model):
    noti_type = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    type = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    main_id = models.IntegerField(blank=True, null=True)
    ref_id = models.IntegerField(blank=True, null=True)
    header = models.TextField(db_collation='Thai_CI_AI')
    detail = models.TextField(db_collation='Thai_CI_AI')
    create_date = models.DateTimeField()
    is_read = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_app_notification'
