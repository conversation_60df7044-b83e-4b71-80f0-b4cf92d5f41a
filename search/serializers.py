from rest_framework import serializers
from .models import TcdSector, TcdSkill, TcdService


class TcdSectorSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล TcdSector (Sector Master Data)
    """
    display_text = serializers.SerializerMethodField()

    class Meta:
        model = TcdSector
        fields = ['id', 'code', 'name_th', 'name_en', 'display_text']
        read_only_fields = ['id']

    def get_display_text(self, obj) -> str:
        """
        สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en
        """
        parts = []
        if obj.code:
            parts.append(obj.code)
        if obj.name_th:
            parts.append(obj.name_th)
        if obj.name_en:
            parts.append(obj.name_en)
        return ' : '.join(parts)


class TcdSkillSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล TcdSkill (Skill Master Data)
    """
    display_text = serializers.SerializerMethodField()
    sector_info = serializers.SerializerMethodField()

    class Meta:
        model = TcdSkill
        fields = ['id', 'sector_id', 'code', 'name_th', 'name_en', 'st', 'nd', 'rd', 'display_text', 'sector_info']
        read_only_fields = ['id']

    def get_display_text(self, obj) -> str:
        """
        สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en
        """
        parts = []
        if obj.code:
            parts.append(obj.code)
        if obj.name_th:
            parts.append(obj.name_th)
        if obj.name_en:
            parts.append(obj.name_en)
        return ' : '.join(parts)

    def get_sector_info(self, obj) -> str:
        """
        ดึงข้อมูลสาขาที่เกี่ยวข้อง
        """
        if obj.sector_id:
            try:
                sector = TcdSector.objects.get(id=obj.sector_id)
                return {
                    'id': sector.id,
                    'code': sector.code,
                    'name_th': sector.name_th,
                    'name_en': sector.name_en,
                    'display_text': f"{sector.code or ''} : {sector.name_th or ''} : {sector.name_en or ''}".strip(' :')
                }
            except TcdSector.DoesNotExist:
                return None
        return None


class TcdServiceSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล TcdService (Service Master Data)
    """
    display_text = serializers.SerializerMethodField()

    class Meta:
        model = TcdService
        fields = ['id', 'code', 'name_th', 'name_en', 'display_text']
        read_only_fields = ['id']

    def get_display_text(self, obj) -> str:
        """
        สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en
        """
        parts = []
        if obj.code:
            parts.append(obj.code)
        if obj.name_th:
            parts.append(obj.name_th)
        if obj.name_en:
            parts.append(obj.name_en)
        return ' : '.join(parts)


class SkillFilterRequestSerializer(serializers.Serializer):
    """
    Serializer สำหรับ request parameters ของการกรองความเชี่ยวชาญ
    """
    sector_ids = serializers.CharField(
        required=True,
        help_text="รายการ sector ID ที่เลือก (คั่นด้วยเครื่องหมายจุลภาค เช่น 1,2,3)"
    )

    def validate_sector_ids(self, value):
        """
        ตรวจสอบและแปลง sector_ids เป็น list ของ integers
        """
        if not value or not value.strip():
            raise serializers.ValidationError("กรุณาระบุ sector_ids")

        try:
            sector_ids = []
            for id_str in value.split(','):
                id_str = id_str.strip()
                if id_str:
                    sector_id = int(id_str)
                    if sector_id <= 0:
                        raise serializers.ValidationError("sector_id ต้องเป็นจำนวนเต็มบวก")
                    sector_ids.append(sector_id)

            if not sector_ids:
                raise serializers.ValidationError("ไม่พบ sector_id ที่ถูกต้อง")

            if len(sector_ids) > 50:
                raise serializers.ValidationError("จำนวน sector_id เกินขีดจำกัด (สูงสุด 50 รายการ)")

            return sector_ids
        except ValueError:
            raise serializers.ValidationError("รูปแบบ sector_ids ไม่ถูกต้อง กรุณาใช้ตัวเลขคั่นด้วยเครื่องหมายจุลภาค")
