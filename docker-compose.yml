services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - LOG_LEVEL=ERROR
      - X-API-Key=xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV
      - DB_SERVICE=MCDC
      - DB_USER=sa
      - DB_PASSWORD=test12345*
      - DB_HOST=**********
      - DB_PORT=1433
      - BASE_URL=
      - MEDIA_PREFIX=
      - UPLOAD_DIR=/app/mcdc/uploads
      - CHAT_SUB_DIR=chat/
      - PAYMENT_SUB_DIR=
      - DOCUMENT_SUB_DIR=
      - DASHBOARD_SUB_DIR=
      - WORKLIST_SUB_DIR=
      - MATCHING_SUB_DIR=
      - BASE_FILE_URL=http://************:8001/
      - WEBSOCKET_HOST=************
      - WEBSOCKET_PORT=8001
      - WEBSOCKET_PROTOCOL=ws
      - SMTP_SERVER=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=ybqolduzriejbtvd
      - TZ=Asia/Bangkok
    volumes:
      - /home/<USER>/uploads:/app/mcdc/uploads
