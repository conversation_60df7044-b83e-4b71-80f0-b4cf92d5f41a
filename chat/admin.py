# -*- coding: utf-8 -*-
"""
Chat admin configuration
"""
from django.contrib import admin
from .models import TcdChat, TcdChatPrompt, TcdSettingChat


@admin.register(TcdChat)
class TcdChatAdmin(admin.ModelAdmin):
    """
    Admin configuration for TcdChat model
    """
    list_display = ['id', 'type', 'message_preview', 'sender_info', 'recipient_info', 'date', 'read_status']
    list_filter = ['type', 'date', 'consult_read', 'users_read']
    search_fields = ['message', 'file_name']
    readonly_fields = ['date']
    ordering = ['-date']

    def message_preview(self, obj):
        """Show message preview"""
        if obj.message:
            return obj.message[:50] + '...' if len(obj.message) > 50 else obj.message
        elif obj.file_name:
            return f"[File: {obj.file_name}]"
        return "[No content]"
    message_preview.short_description = 'Message'

    def sender_info(self, obj):
        """Show sender information"""
        if obj.users:
            return f"Member: {obj.users.username}"
        elif obj.user_consult:
            return f"Consultant: {obj.user_consult.username}"
        return "Unknown"
    sender_info.short_description = 'Sender'

    def recipient_info(self, obj):
        """Show recipient information"""
        if obj.users and obj.user_consult:
            if obj.users:
                return f"Consultant: {obj.user_consult.username}"
            else:
                return f"Member: {obj.users.username}"
        return "Unknown"
    recipient_info.short_description = 'Recipient'

    def read_status(self, obj):
        """Show read status"""
        status = []
        if obj.users_read == '1':
            status.append('Staff Read')
        if obj.consult_read == '1':
            status.append('Consultant Read')
        return ', '.join(status) if status else 'Unread'
    read_status.short_description = 'Read Status'


@admin.register(TcdChatPrompt)
class TcdChatPromptAdmin(admin.ModelAdmin):
    """
    Admin configuration for TcdChatPrompt model
    """
    list_display = ['id', 'question_th', 'question_en', 'status', 'create_date', 'update_date']
    list_filter = ['status', 'create_date', 'update_date']
    search_fields = ['question_th', 'question_en', 'answer_th', 'answer_en']
    readonly_fields = ['create_date', 'update_date']

    fieldsets = (
        ('Thai Content', {
            'fields': ('question_th', 'answer_th')
        }),
        ('English Content', {
            'fields': ('question_en', 'answer_en')
        }),
        ('Settings', {
            'fields': ('status',)
        }),
        ('Metadata', {
            'fields': ('create_user_id', 'update_user_id', 'create_date', 'update_date'),
            'classes': ('collapse',)
        })
    )


@admin.register(TcdSettingChat)
class TcdSettingChatAdmin(admin.ModelAdmin):
    """
    Admin configuration for TcdSettingChat model
    """
    list_display = ['id', 'start_time', 'end_time', 'detail_th', 'status']
    list_filter = ['status']
    search_fields = ['detail_th', 'detail_en']

    fieldsets = (
        ('Time Settings', {
            'fields': ('start_time', 'end_time')
        }),
        ('Details', {
            'fields': ('detail_th', 'detail_en')
        }),
        ('Status', {
            'fields': ('status',)
        })
    )


# Note: TcdChatRoom and TcdChatSession models were removed to use existing database tables only
# All chat functionality now uses the existing TcdChat model
