# -*- coding: utf-8 -*-
"""
Chat services for business logic
"""
import logging
from django.utils import timezone
from django.db.models import Q, Max
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from .models import TcdChat, TcdChatPrompt, TcdSettingChat
from authentication.models import TcdAppMember, TcdUserConsult
from MCDC.models import TcdUserConsult as UserConsult
from .utils import save_uploaded_file, determine_message_type, get_file_url
from django.conf import settings

logger = logging.getLogger(__name__)


class ChatService:
    """
    Service class for chat operations
    """
    
    @staticmethod
    def create_message(sender_id, sender_type, message=None, file_name=None, file_src=None,
                      recipient_id=None, recipient_type=None, message_type='M', uploaded_file=None):
        """
        Create a new chat message with enhanced file upload handling

        Args:
            sender_id: ID of the sender
            sender_type: Type of sender ('member' or 'consultant')
            message: Text message content
            file_name: Name of attached file (will be overridden if uploaded_file is provided)
            file_src: Source path of attached file (will be overridden if uploaded_file is provided)
            recipient_id: ID of the recipient
            recipient_type: Type of recipient ('member' or 'consultant')
            message_type: Type of message ('M', 'F', 'I', 'D') - will be auto-determined if uploaded_file is provided
            uploaded_file: Django UploadedFile object for file uploads

        Returns:
            dict: {'success': bool, 'message': TcdChat object or None, 'error': str or None}
        """
        try:
            # Handle file upload if provided
            if uploaded_file:
                # Determine file type (image or document)
                file_type = 'image' if uploaded_file.content_type.startswith('image/') else 'document'

                # Save the uploaded file
                save_result = save_uploaded_file(uploaded_file, file_type)
                if not save_result['success']:
                    return {
                        'success': False,
                        'message': None,
                        'error': save_result['error']
                    }

                # Override file parameters with actual upload results
                file_name = uploaded_file.name
                file_src = save_result['file_path']

                # Set message type based on file type
                if file_type == 'image':
                    message_type = 'I'
                else:
                    message_type = 'D'

            # Determine user_consult_id and users_id based on sender and recipient
            if sender_type == 'member':
                users_id = sender_id
                user_consult_id = recipient_id if recipient_type == 'consultant' else None
            else:  # consultant
                user_consult_id = sender_id
                users_id = recipient_id if recipient_type == 'member' else None

            # Create the message
            chat_message = TcdChat.objects.create(
                type=message_type,
                message=message or '',
                file_name=file_name,
                file_src=file_src,
                user_consult_id=user_consult_id,
                users_id=users_id,
                consult_read='0',  # Default: unread
                users_read='0',   # Default: unread
                date=timezone.now()
            )

            logger.info(f"Created chat message: {chat_message.id} with type: {message_type}")
            return {
                'success': True,
                'message': chat_message,
                'error': None
            }

        except Exception as e:
            logger.error(f"Error creating chat message: {str(e)}")
            return {
                'success': False,
                'message': None,
                'error': f"Failed to create message: {str(e)}"
            }
    
    @staticmethod
    def get_chat_history(user_id, consult_id=None, limit=20, offset=0):
        """
        Get chat history for a user
        
        Args:
            user_id: ID of the requesting user
            user_type: Type of requesting user ('member' or 'consultant')
            recipient_id: ID of the chat partner
            recipient_type: Type of chat partner
            consult_id: Consultation ID
            limit: Number of messages to retrieve
            offset: Offset for pagination
        
        Returns:
            QuerySet: Chat messages
        """
        try:
            queryset = TcdChat.objects.all()
            queryset = queryset.filter(user_consult_id=user_id)
            
            if consult_id:
                queryset = queryset.filter(users_id=consult_id)
            
            return queryset.order_by('-date')[offset:offset + limit]
            
        except Exception as e:
            logger.error(f"Error getting chat history: {str(e)}")
            raise
    
    @staticmethod
    def mark_messages_as_read(message_ids, user_id, user_type):
        """
        Mark messages as read for a user
        
        Args:
            message_ids: List of message IDs to mark as read
            user_id: ID of the user marking messages as read
            user_type: Type of user ('member' or 'consultant')
        
        Returns:
            int: Number of messages updated
        """
        try:
            if user_type == 'member':
                updated = TcdChat.objects.filter(
                    id__in=message_ids,
                    users_id=user_id
                ).update(users_read='1')  # Read status
            else:  # consultant
                updated = TcdChat.objects.filter(
                    id__in=message_ids,
                    user_consult_id=user_id
                ).update(consult_read='1')  # Read status
            
            logger.info(f"Marked {updated} messages as read for user {user_id}")
            return updated
            
        except Exception as e:
            logger.error(f"Error marking messages as read: {str(e)}")
            raise
    
    @staticmethod
    def get_unread_count(user_id, user_type, recipient_id=None, recipient_type=None):
        """
        Get unread message count for a user
        
        Args:
            user_id: ID of the user
            user_type: Type of user ('member' or 'consultant')
            recipient_id: ID of the chat partner (optional)
            recipient_type: Type of chat partner (optional)
        
        Returns:
            int: Number of unread messages
        """
        try:
            queryset = TcdChat.objects.all()
            
            if user_type == 'member':
                queryset = queryset.filter(users_id=user_id, users_read='N')
                if recipient_id and recipient_type == 'consultant':
                    queryset = queryset.filter(user_consult_id=recipient_id)
            else:  # consultant
                queryset = queryset.filter(user_consult_id=user_id, consult_read='N')
                if recipient_id and recipient_type == 'member':
                    queryset = queryset.filter(users_id=recipient_id)
            
            return queryset.count()
            
        except Exception as e:
            logger.error(f"Error getting unread count: {str(e)}")
            return 0
    
    @staticmethod
    def send_realtime_message(room_name, message_data):
        """
        Send real-time message through WebSocket
        
        Args:
            room_name: Name of the chat room
            message_data: Message data to send
        """
        try:
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                room_name,
                {
                    'type': 'chat_message',
                    **message_data
                }
            )
            logger.info(f"Sent real-time message to room: {room_name}")
            
        except Exception as e:
            logger.error(f"Error sending real-time message: {str(e)}")
    
    @staticmethod
    def get_active_chat_settings():
        """
        Get active chat settings
        
        Returns:
            TcdSettingChat: Active chat settings or None
        """
        try:
            return TcdSettingChat.objects.filter(status=True).first()
        except Exception as e:
            logger.error(f"Error getting chat settings: {str(e)}")
            return None
    
    @staticmethod
    def get_chat_prompts(language='th'):
        """
        Get active chat prompts
        
        Args:
            language: Language preference ('th' or 'en')
        
        Returns:
            QuerySet: Active chat prompts
        """
        try:
            return TcdChatPrompt.objects.filter(status=True).order_by('id')
        except Exception as e:
            logger.error(f"Error getting chat prompts: {str(e)}")
            return TcdChatPrompt.objects.none()
    
    @staticmethod
    def is_chat_time_allowed():
        """
        Check if current time is within allowed chat hours

        Returns:
            bool: True if chat is allowed, False otherwise
        """
        try:
            settings = ChatService.get_active_chat_settings()
            if not settings:
                return True  # Allow chat if no settings found

            current_time = timezone.now().time()
            return settings.start_time <= current_time <= settings.end_time

        except Exception as e:
            logger.error(f"Error checking chat time: {str(e)}")
            return True  # Allow chat on error

    @staticmethod
    def get_file_access_info(message_id, user_id, user_type):
        """
        Get file access information for a chat message

        Args:
            message_id: ID of the chat message
            user_id: ID of the requesting user
            user_type: Type of user ('consultant' or 'staff')

        Returns:
            dict: File access information with permissions and metadata
        """
        try:
            # Get the chat message
            try:
                chat_message = TcdChat.objects.get(id=message_id)
            except TcdChat.DoesNotExist:
                return {
                    'success': False,
                    'error': 'Message not found',
                    'has_access': False
                }

            # Check if message has a file
            if not chat_message.file_src or not chat_message.file_name:
                return {
                    'success': False,
                    'error': 'Message does not contain a file',
                    'has_access': False
                }

            # Check user permissions
            has_access = False

            if user_type == 'consultant':
                # Consultant can access files from their own room
                has_access = (chat_message.user_consult_id == user_id)
            else:
                # Staff can access files from rooms they participate in
                staff_participation = TcdChat.objects.filter(
                    user_consult_id=chat_message.user_consult_id,
                    users_id=user_id
                ).exists()
                has_access = staff_participation

            if not has_access:
                return {
                    'success': False,
                    'error': 'Access denied',
                    'has_access': False
                }

            # Build file information
            import os
            from django.conf import settings

            upload_dir = getattr(settings, 'UPLOAD_DIR', 'uploads')
            file_path = os.path.join(upload_dir, chat_message.file_src)
            file_exists = os.path.exists(file_path)
            file_size = os.path.getsize(file_path) if file_exists else 0

            # Determine content type
            import mimetypes
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                if chat_message.type == 'I':
                    content_type = 'image/jpeg'
                else:
                    content_type = 'application/octet-stream'

            return {
                'success': True,
                'has_access': True,
                'message': chat_message,
                'file_info': {
                    'message_id': chat_message.id,
                    'file_name': chat_message.file_name,
                    'file_src': chat_message.file_src,
                    'file_path': file_path,
                    'file_type': 'image' if chat_message.type == 'I' else 'document',
                    'message_type': chat_message.type,
                    'content_type': content_type,
                    'file_size': file_size,
                    'file_exists': file_exists,
                    'download_url': f"/api/chat/file/download/{chat_message.id}/",
                    'metadata_url': f"/api/chat/file/metadata/{chat_message.id}/",
                    'full_url': get_file_url(chat_message.file_src) if file_exists else None,
                    'uploaded_date': chat_message.date.isoformat() if chat_message.date else None,
                    'is_image': chat_message.type == 'I',
                    'is_document': chat_message.type == 'D'
                }
            }

        except Exception as e:
            logger.error(f"Error getting file access info: {str(e)}")
            return {
                'success': False,
                'error': f'Server error: {str(e)}',
                'has_access': False
            }

    @staticmethod
    def get_chat_files(user_id, user_type, consult_id=None, file_type=None, limit=20, offset=0):
        """
        Get list of files from chat messages with proper access control

        Args:
            user_id: ID of the requesting user
            user_type: Type of user ('consultant' or 'staff')
            consult_id: Filter by consultation ID (optional)
            file_type: Filter by file type ('I' for images, 'D' for documents, optional)
            limit: Number of files to retrieve
            offset: Offset for pagination

        Returns:
            dict: List of accessible files with metadata
        """
        try:
            # Build base query for messages with files
            queryset = TcdChat.objects.filter(
                file_src__isnull=False,
                file_name__isnull=False
            ).exclude(
                file_src='',
                file_name=''
            )

            # Apply access control
            if user_type == 'consultant':
                # Consultant can only see files from their own room
                queryset = queryset.filter(user_consult_id=user_id)
            else:
                # Staff can see files from rooms they participate in
                # Get consultations where this staff member has participated
                participated_consultations = TcdChat.objects.filter(
                    users_id=user_id
                ).values_list('user_consult_id', flat=True).distinct()

                queryset = queryset.filter(user_consult_id__in=participated_consultations)

            # Apply filters
            if consult_id:
                queryset = queryset.filter(user_consult_id=consult_id)

            if file_type:
                queryset = queryset.filter(type=file_type)

            # Order by date descending and paginate
            queryset = queryset.order_by('-date')[offset:offset + limit]

            # Build file list with metadata
            files = []
            for message in queryset:
                file_access = ChatService.get_file_access_info(message.id, user_id, user_type)
                if file_access['success'] and file_access['has_access']:
                    files.append(file_access['file_info'])

            return {
                'success': True,
                'files': files,
                'total_count': len(files),
                'has_more': len(queryset) == limit
            }

        except Exception as e:
            logger.error(f"Error getting chat files: {str(e)}")
            return {
                'success': False,
                'error': f'Server error: {str(e)}',
                'files': []
            }


class MobileChatService:
    """
    Service class for mobile chat operations using existing TcdChat model
    """

    @staticmethod
    def create_or_get_chat_room(member_id, consultant_id, room_type='consultation'):
        """
        Create or get chat room info for mobile application using existing TcdChat model

        Args:
            member_id: ID of the member
            consultant_id: ID of the consultant
            room_type: Type of chat room

        Returns:
            dict: Chat room information
        """
        try:
            room_id = f"{room_type}_{member_id}_{consultant_id}"

            # Get member and consultant info
            member = TcdAppMember.objects.get(id=member_id)
            consultant = TcdUserConsult.objects.get(id=consultant_id)

            room_name = f"Chat: {member.first_name} {member.last_name} & {consultant.name}"

            # Check if chat history exists
            existing_chat = TcdChat.objects.filter(
                users_id=member_id,
                user_consult_id=consultant_id
            ).first()

            created = existing_chat is None

            room_info = {
                'room_id': room_id,
                'room_name': room_name,
                'room_type': room_type,
                'member_id': member_id,
                'consultant_id': consultant_id,
                'is_active': True,
                'created': created
            }

            logger.info(f"{'Created' if created else 'Retrieved'} chat room info: {room_id}")
            return room_info

        except Exception as e:
            logger.error(f"Error creating/getting chat room: {str(e)}")
            raise

    @staticmethod
    def create_chat_session(room_info, user_id, user_type, device_token=None):
        """
        Create or update chat session info (simplified for existing models)

        Args:
            room_info: Room information dict
            user_id: ID of the user
            user_type: Type of user ('member' or 'consultant')
            device_token: Device token for push notifications

        Returns:
            dict: Chat session information
        """
        try:
            session_id = f"{room_info['room_id']}_{user_id}_{user_type}"

            session_info = {
                'session_id': session_id,
                'room_id': room_info['room_id'],
                'user_id': user_id,
                'user_type': user_type,
                'is_online': True,
                'device_token': device_token,
                'created_at': timezone.now().isoformat()
            }

            logger.info(f"Created chat session info: {session_id}")
            return session_info

        except Exception as e:
            logger.error(f"Error creating chat session: {str(e)}")
            raise

    @staticmethod
    def get_user_chat_rooms(user_id, user_type):
        """
        Get chat rooms for a user using existing TcdChat model

        Args:
            user_id: ID of the user
            user_type: Type of user ('member' or 'consultant')

        Returns:
            list: Chat room information list
        """
        try:
            rooms = []
            if user_type == 'member':
                # Get distinct consultants that member has chatted with
                consultations = TcdChat.objects.filter(
                    users_id=user_id
                ).values('user_consult_id').distinct()

                for consult in consultations:
                    if consult['user_consult_id']:
                        rooms.append({
                            'room_id': f"consultation_{user_id}_{consult['user_consult_id']}",
                            'member_id': user_id,
                            'consultant_id': consult['user_consult_id'],
                            'room_type': 'consultation'
                        })
            else:  # consultant
                # Get distinct members that consultant has chatted with
                member_chats = TcdChat.objects.filter(
                    user_consult_id=user_id
                ).values('users_id').distinct()

                for chat in member_chats:
                    if chat['users_id']:
                        rooms.append({
                            'room_id': f"consultation_{chat['users_id']}_{user_id}",
                            'member_id': chat['users_id'],
                            'consultant_id': user_id,
                            'room_type': 'consultation'
                        })

            return rooms

        except Exception as e:
            logger.error(f"Error getting user chat rooms: {str(e)}")
            return []

    @staticmethod
    def get_room_messages(room_id, limit=50, offset=0):
        """
        Get messages for a chat room using existing TcdChat model

        Args:
            room_id: ID of the chat room (format: consultation_member_id_consultant_id)
            limit: Number of messages to retrieve
            offset: Offset for pagination

        Returns:
            QuerySet: Chat messages
        """
        try:
            # Parse room_id to get member_id and consultant_id
            parts = room_id.split('_')
            if len(parts) >= 3:
                member_id = int(parts[1])
                consultant_id = int(parts[2])
            else:
                logger.error(f"Invalid room_id format: {room_id}")
                return TcdChat.objects.none()

            messages = TcdChat.objects.filter(
                users_id=member_id,
                user_consult_id=consultant_id
            ).order_by('-date')[offset:offset + limit]

            return messages

        except Exception as e:
            logger.error(f"Error getting room messages: {str(e)}")
            return TcdChat.objects.none()

    @staticmethod
    def send_push_notification(device_tokens, title, body, data=None):
        """
        Send push notification to mobile devices

        Args:
            device_tokens: List of device tokens
            title: Notification title
            body: Notification body
            data: Additional data

        Returns:
            bool: Success status
        """
        try:
            # This would integrate with your push notification service
            # (Firebase, APNs, etc.)
            logger.info(f"Sending push notification to {len(device_tokens)} devices")
            logger.info(f"Title: {title}, Body: {body}")

            # Implementation would depend on your push notification provider
            # For now, just log the notification
            return True

        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
            return False

    @staticmethod
    def update_user_online_status(user_id, user_type, is_online):
        """
        Update user online status (simplified for existing models)

        Args:
            user_id: ID of the user
            user_type: Type of user
            is_online: Online status

        Returns:
            int: Number of sessions updated (simulated)
        """
        try:
            # For now, just log the status change since we don't have session tracking
            logger.info(f"User {user_id} ({user_type}) online status: {is_online}")
            # In a real implementation, you might use cache or another mechanism
            return 1  # Simulate one session updated

        except Exception as e:
            logger.error(f"Error updating online status: {str(e)}")
            return 0

    @staticmethod
    def get_unread_messages_count(user_id, user_type, room_id=None):
        """
        Get unread messages count for a user

        Args:
            user_id: ID of the user
            user_type: Type of user
            room_id: Specific room ID (optional)

        Returns:
            int: Number of unread messages
        """
        try:
            queryset = TcdChat.objects.all()

            if user_type == 'member':
                queryset = queryset.filter(users_id=user_id, users_read='0')  # Unread status
            else:  # consultant
                queryset = queryset.filter(user_consult_id=user_id, consult_read='0')  # Unread status

            if room_id:
                # Parse room_id to get member_id and consultant_id
                parts = room_id.split('_')
                if len(parts) >= 3:
                    member_id = int(parts[1])
                    consultant_id = int(parts[2])
                    queryset = queryset.filter(
                        users_id=member_id,
                        user_consult_id=consultant_id
                    )

            return queryset.count()

        except Exception as e:
            logger.error(f"Error getting unread count: {str(e)}")
            return 0
