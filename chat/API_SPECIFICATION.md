# Chat System API Specification

## Overview

This document provides comprehensive API specification for the Chat System, including REST API endpoints and WebSocket functionality. The system supports real-time messaging between consultants (TcdUserConsult) and staff (TcdUsers), with complete blocking of regular members (TcdAppMembers).

## Authentication

### JWT Token Authentication
All API endpoints and WebSocket connections require JWT authentication using RS256 algorithm.

**Token Requirements:**
- **Algorithm**: RS256
- **Header**: `Authorization: Bearer <token>`
- **WebSocket**: Token passed as query parameter `?token=<jwt_token>`

**User Types:**
- `consultant` - TcdUserConsult (can own chat rooms)
- `staff` - TcdUsers (can join consultant rooms)
- `member` - TcdAppMembers (**BLOCKED** from all chat features)

## REST API Endpoints

### Base URL
```
/api/chat/
```

### 1. Start Chat
**Endpoint:** `POST /api/chat/start/`

**Description:** Initialize a new chat session or join existing room

**Authentication:** Required (Consultant/Staff only)

**Request Body:**
```json
{
    "consultant_id": 123,  // Required for staff
    "chat_type": "consultation",
    "device_token": "fcm_device_token_here"  // Optional for push notifications
}
```

**Response (Success - 201):**
```json
{
    "success": true,
    "data": {
        "room_id": "consultant_123",
        "room_name": "Dr. John Doe - Consultation",
        "room_type": "consultation",
        "participant_info": [
            {
                "id": 123,
                "name": "Dr. John Doe",
                "type": "consultant",
                "is_owner": true
            }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "allows_any_staff": true
    },
    "message": "Chat room created successfully"
}
```

**Error Responses:**
- `400` - Invalid request data
- `401` - Authentication required
- `403` - Access denied (members blocked)

### 2. Get Chat Rooms
**Endpoint:** `GET /api/chat/rooms/`

**Description:** Get list of available chat rooms for the authenticated user

**Authentication:** Required (Consultant/Staff only)

**Query Parameters:**
```
page=1          // Page number (default: 1)
per_page=20     // Items per page (default: 20, max: 100)
```

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "rooms": [
            {
                "room_id": "consultant_123",
                "room_name": "Dr. John Doe - Consultation",
                "room_type": "consultation",
                "owner": {
                    "id": 123,
                    "name": "Dr. John Doe",
                    "type": "consultant"
                },
                "participant_info": [
                    {
                        "id": 123,
                        "name": "Dr. John Doe",
                        "type": "consultant",
                        "is_owner": true
                    },
                    {
                        "id": 456,
                        "name": "Jane Smith",
                        "type": "staff",
                        "is_owner": false
                    }
                ],
                "last_message": {
                    "id": 789,
                    "message": "Thank you for your consultation",
                    "sender_name": "Jane Smith",
                    "sender_type": "staff",
                    "date": "2024-01-15T10:30:00Z",
                    "file_name": null
                },
                "unread_count": 2,
                "created_at": "2024-01-15T09:00:00Z",
                "total_participants": 2,
                "allows_any_staff": true
            }
        ]
    },
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 5,
        "has_next": false
    }
}
```

### 3. Send Message
**Endpoint:** `POST /api/chat/send/`

**Description:** Send a text message or file to a chat room

**Authentication:** Required (Consultant/Staff only)

**Content-Type:** `multipart/form-data` (for file uploads) or `application/json`

**Request Body (Text Message):**
```json
{
    "message": "Hello, how can I help you today?",
    "message_type": "T",  // T=Text, I=Image, D=Document
    "recipient_id": 456,  // Optional - specific recipient
    "recipient_type": "staff"  // "staff" or "consultant"
}
```

**Request Body (File Upload):**
```
message: "Please review this document"  // Optional text with file
file: <binary_file_data>  // File upload
message_type: "D"  // I=Image, D=Document
recipient_id: 456  // Optional
recipient_type: "staff"
```

**Response (Success - 201):**
```json
{
    "success": true,
    "data": {
        "message": {
            "id": 789,
            "type": "T",
            "message": "Hello, how can I help you today?",
            "file_name": null,
            "file_src": null,
            "user_consult": 123,
            "users": null,
            "consult_read": "0",
            "users_read": "0",
            "date": "2024-01-15T10:30:00Z",
            "sender_name": "Dr. John Doe",
            "sender_type": "consultant",
            "file_info": null,
            "has_file": false
        }
    },
    "message": "Message sent successfully"
}
```

**File Upload Response:**
```json
{
    "success": true,
    "data": {
        "message": {
            "id": 790,
            "type": "D",
            "message": "Please review this document",
            "file_name": "report.pdf",
            "file_src": "http://localhost:8000/media/documents/abc123.pdf",
            "user_consult": 123,
            "users": null,
            "consult_read": "0",
            "users_read": "0",
            "date": "2024-01-15T10:35:00Z",
            "sender_name": "Dr. John Doe",
            "sender_type": "consultant",
            "file_info": {
                "file_name": "report.pdf",
                "file_src": "documents/abc123.pdf",
                "file_type": "document",
                "message_type": "D",
                "content_type": "application/pdf",
                "file_size": 1048576,
                "file_exists": true,
                "download_url": "/api/chat/file/download/790/",
                "metadata_url": "/api/chat/file/metadata/790/",
                "full_url": "http://localhost:8000/media/documents/abc123.pdf",
                "is_image": false,
                "is_document": true
            },
            "has_file": true
        }
    },
    "message": "Message with file sent successfully"
}
```

**File Upload Restrictions:**
- **Images**: JPEG, JPG, PNG - Max 1MB
- **Documents**: PDF, DOC, DOCX, XLS, XLSX - Max 5MB

### 4. Get Chat History
**Endpoint:** `GET /api/chat/history/`

**Description:** Retrieve chat message history with pagination

**Authentication:** Required (Consultant/Staff only)

**Query Parameters:**
```
user_consult_id=123  // Required for staff, ignored for consultants
page=1               // Page number (default: 1)
per_page=20          // Items per page (default: 20, max: 100)
date_from=2024-01-01T00:00:00Z  // Optional filter
date_to=2024-01-31T23:59:59Z    // Optional filter
```

**Access Control:**
- **Consultants**: Access their own chat history automatically (no user_consult_id needed)
- **Staff**: Must provide user_consult_id parameter to specify which consultant's room

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "messages": [
            {
                "id": 789,
                "type": "T",
                "message": "Hello, how can I help you today?",
                "file_name": null,
                "file_src": null,
                "user_consult": 123,
                "users": null,
                "consult_read": "1",
                "users_read": "0",
                "date": "2024-01-15T10:30:00Z",
                "sender_name": "Dr. John Doe",
                "sender_type": "consultant",
                "file_info": null,
                "has_file": false
            },
            {
                "id": 790,
                "type": "I",
                "message": "Here's the X-ray image",
                "file_name": "xray.jpg",
                "file_src": "http://localhost:8000/media/images/def456.jpg",
                "user_consult": null,
                "users": 456,
                "consult_read": "0",
                "users_read": "1",
                "date": "2024-01-15T10:32:00Z",
                "sender_name": "Jane Smith",
                "sender_type": "staff",
                "file_info": {
                    "file_name": "xray.jpg",
                    "file_src": "images/def456.jpg",
                    "file_type": "image",
                    "message_type": "I",
                    "content_type": "image/jpeg",
                    "file_size": 524288,
                    "file_exists": true,
                    "download_url": "/api/chat/file/download/790/",
                    "metadata_url": "/api/chat/file/metadata/790/",
                    "full_url": "http://localhost:8000/media/images/def456.jpg",
                    "is_image": true,
                    "is_document": false
                },
                "has_file": true
            }
        ]
    },
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 25,
        "has_next": true
    }
}
```

### 5. Mark Messages as Read
**Endpoint:** `POST /api/chat/mark-read/`

**Description:** Mark specific messages as read for the authenticated user

**Authentication:** Required (Consultant/Staff only)

**Request Body:**
```json
{
    "message_ids": [789, 790, 791],  // Array of message IDs to mark as read
    "user_consult_id": 123  // Required for staff, ignored for consultants
}
```

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "marked_count": 3,
        "message_ids": [789, 790, 791]
    },
    "message": "Messages marked as read successfully"
}
```

### 6. Mark All Messages as Read
**Endpoint:** `POST /api/chat/mark-all-read/`

**Description:** Mark all unread messages as read for the authenticated user

**Authentication:** Required (Consultant/Staff only)

**Access Control:**
- **Consultants**: Marks all unread messages in their own room as read (consult_read = '1')
- **Staff**: Marks all unread messages as read for a specific consultant's room (users_read = '1')

**Request Body (Consultant):**
```json
{}  // Empty body - consultant marks their own messages
```

**Request Body (Staff):**
```json
{
    "user_consult_id": 123  // Required - ID of consultant whose messages to mark as read
}
```

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "updated_count": 15,
        "user_type": "consultant",
        "message": "Marked 15 messages as read"
    }
}
```

**Error Responses:**
- `400` - Missing user_consult_id for staff users
- `401` - Unauthorized access (members blocked, or staff without room access)
- `404` - Consultant not found (invalid user_consult_id)
- `403` - Access denied

### 7. Update Online Status
**Endpoint:** `POST /api/chat/online-status/`

**Description:** Update user's online status in the chat system

**Authentication:** Required (Consultant/Staff only)

**Request Body:**
```json
{
    "is_online": true,
    "status_message": "Available for consultation"  // Optional
}
```

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "user_id": 123,
        "user_type": "consultant",
        "is_online": true,
        "last_seen": "2024-01-15T10:30:00Z",
        "status_message": "Available for consultation"
    },
    "message": "Online status updated successfully"
}
```

### 8. List Chat Files
**Endpoint:** `GET /api/chat/files/`

**Description:** Get list of files shared in chat conversations

**Authentication:** Required (Consultant/Staff only)

**Query Parameters:**
```
user_consult_id=123  // Required for staff, ignored for consultants
file_type=image      // Optional: "image", "document", or "all" (default)
page=1               // Page number (default: 1)
per_page=20          // Items per page (default: 20, max: 100)
```

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "files": [
            {
                "message_id": 790,
                "file_name": "xray.jpg",
                "file_type": "image",
                "content_type": "image/jpeg",
                "file_size": 524288,
                "upload_date": "2024-01-15T10:32:00Z",
                "sender_name": "Jane Smith",
                "sender_type": "staff",
                "download_url": "/api/chat/file/download/790/",
                "full_url": "http://localhost:8000/media/images/def456.jpg"
            }
        ]
    },
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 8,
        "has_next": false
    }
}
```

### 9. Download Chat File
**Endpoint:** `GET /api/chat/file/download/<message_id>/`

**Description:** Download a file attachment from a chat message

**Authentication:** Required (Consultant/Staff only)

**Path Parameters:**
- `message_id` - ID of the message containing the file

**Response (Success - 200):**
- **Content-Type**: Based on file type (e.g., `image/jpeg`, `application/pdf`)
- **Content-Disposition**: `attachment; filename="original_filename.ext"`
- **Body**: Binary file data

**Error Responses:**
- `404` - Message or file not found
- `403` - Access denied to this file

### 10. Get File Metadata
**Endpoint:** `GET /api/chat/file/metadata/<message_id>/`

**Description:** Get metadata information about a file attachment

**Authentication:** Required (Consultant/Staff only)

**Response (Success - 200):**
```json
{
    "success": true,
    "data": {
        "message_id": 790,
        "file_name": "xray.jpg",
        "file_type": "image",
        "content_type": "image/jpeg",
        "file_size": 524288,
        "file_exists": true,
        "upload_date": "2024-01-15T10:32:00Z",
        "sender_name": "Jane Smith",
        "sender_type": "staff",
        "download_url": "/api/chat/file/download/790/",
        "full_url": "http://localhost:8000/media/images/def456.jpg",
        "dimensions": {
            "width": 1024,
            "height": 768
        }  // Only for images
    }
}
```

## WebSocket API

### Connection URLs

**Mobile Chat Room:**
```
{WEBSOCKET_BASE_URL}/ws/chat/room/<room_id>/?token=<jwt_token>
```

**Chat Session:**
```
{WEBSOCKET_BASE_URL}/ws/chat/session/<session_id>/?token=<jwt_token>
```

**Default URLs (Development):**
- Room: `ws://localhost:8080/ws/chat/room/<room_id>/?token=<jwt_token>`
- Session: `ws://localhost:8080/ws/chat/session/<session_id>/?token=<jwt_token>`

**Configuration:**
WebSocket URLs are configurable via environment variables:
- `WEBSOCKET_HOST`: Server hostname (default: `localhost`)
- `WEBSOCKET_PORT`: Server port (default: `8080`)
- `WEBSOCKET_PROTOCOL`: Protocol (default: `ws`, use `wss` for SSL)

### Authentication

WebSocket connections require JWT token authentication:
- **Query Parameter**: `?token=<jwt_token>`
- **Validation**: Token is validated on connection
- **Access Control**: Same rules as REST API (consultants/staff only, members blocked)

### Connection Flow

1. **Client connects** with JWT token
2. **Server validates** authentication and chat access
3. **Server validates** room access permissions
4. **Connection established** or rejected with error code
5. **Client receives** connection confirmation message

### WebSocket Message Types

#### 1. Connection Established
**Direction:** Server → Client

**Message:**
```json
{
    "type": "connection_established",
    "room_id": "consultant_123",
    "user_type": "consultant",
    "message": "Successfully connected to chat room"
}
```

#### 2. Send Message
**Direction:** Client → Server

**Text Message:**
```json
{
    "type": "message",
    "message": "Hello, how can I help you?"
}
```

**File Message:**
```json
{
    "type": "message",
    "message": "Please review this document",
    "file_data": {
        "name": "report.pdf",
        "data": "base64_encoded_file_data",
        "type": "application/pdf"
    }
}
```

#### 3. Receive Message
**Direction:** Server → Client

**Message:**
```json
{
    "type": "message",
    "data": {
        "id": 789,
        "message": "Hello, how can I help you?",
        "file_name": null,
        "file_src": null,
        "user_id": 123,
        "user_type": "consultant",
        "sender_name": "Dr. John Doe",
        "timestamp": "2024-01-15T10:30:00Z",
        "room_id": "consultant_123",
        "consultant_id": 123
    }
}
```

**File Message:**
```json
{
    "type": "message",
    "data": {
        "id": 790,
        "message": "Please review this document",
        "file_name": "report.pdf",
        "file_src": "http://localhost:8000/media/documents/abc123.pdf",
        "user_id": 123,
        "user_type": "consultant",
        "sender_name": "Dr. John Doe",
        "timestamp": "2024-01-15T10:35:00Z",
        "room_id": "consultant_123",
        "consultant_id": 123
    }
}
```

#### 4. Typing Indicator
**Direction:** Client → Server

```json
{
    "type": "typing",
    "user_id": 123,
    "user_type": "consultant",
    "is_typing": true
}
```

**Direction:** Server → Client

```json
{
    "type": "typing",
    "user_id": 123,
    "user_type": "consultant",
    "is_typing": true
}
```

#### 5. Read Receipt
**Direction:** Client → Server

```json
{
    "type": "read_receipt",
    "message_id": 789,
    "user_id": 456,
    "user_type": "staff"
}
```

**Direction:** Server → Client

```json
{
    "type": "read_receipt",
    "message_id": 789,
    "user_id": 456,
    "user_type": "staff"
}
```

#### 6. User Joined
**Direction:** Client → Server

```json
{
    "type": "join_room",
    "user_id": 456,
    "user_type": "staff"
}
```

**Direction:** Server → Client

```json
{
    "type": "user_joined",
    "user_id": 456,
    "user_type": "staff"
}
```

#### 7. Error Messages
**Direction:** Server → Client

**Authentication Error:**
```json
{
    "type": "error",
    "error_code": "authentication_required",
    "message": "Authentication required",
    "details": "JWT token is missing or invalid"
}
```

**Access Denied:**
```json
{
    "type": "error",
    "error_code": "access_denied",
    "message": "Access denied for this user type",
    "details": "Members are not allowed to access chat features"
}
```

**Room Access Denied:**
```json
{
    "type": "error",
    "error_code": "room_access_denied",
    "message": "Room access denied",
    "details": "You don't have permission to access this room"
}
```

**Message Permission Denied:**
```json
{
    "type": "error",
    "error_code": "message_permission_denied",
    "message": "Message permission denied",
    "details": "You don't have permission to send messages in this room"
}
```

**Invalid Message:**
```json
{
    "type": "error",
    "error_code": "invalid_message",
    "message": "Invalid message format",
    "details": "Message content or file is required"
}
```

**Server Error:**
```json
{
    "type": "error",
    "error_code": "server_error",
    "message": "Internal server error",
    "details": "Failed to save message"
}
```

### WebSocket Connection Codes

- **1000** - Normal closure
- **4001** - Unauthorized (invalid or missing token)
- **4003** - Forbidden (access denied, member blocked)
- **4004** - Not found (room not found)
- **5000** - Internal server error

## Error Handling

### HTTP Status Codes

- **200** - Success
- **201** - Created (message sent, room created)
- **400** - Bad Request (invalid data, validation errors)
- **401** - Unauthorized (authentication required)
- **403** - Forbidden (access denied, members blocked)
- **404** - Not Found (room, message, or file not found)
- **413** - Payload Too Large (file size exceeded)
- **415** - Unsupported Media Type (invalid file type)
- **422** - Unprocessable Entity (validation errors)
- **500** - Internal Server Error

### Error Response Format

All error responses follow this format:

```json
{
    "success": false,
    "error": {
        "code": "validation_error",
        "message": "Validation failed",
        "details": {
            "field_name": ["Error message for this field"]
        }
    }
}
```

### Common Error Codes

- `authentication_required` - JWT token missing or invalid
- `access_denied` - User type not allowed (members blocked)
- `validation_error` - Request data validation failed
- `room_not_found` - Chat room does not exist
- `message_not_found` - Message does not exist
- `file_not_found` - File attachment not found
- `file_too_large` - File exceeds size limit
- `invalid_file_type` - File type not supported
- `permission_denied` - Insufficient permissions
- `server_error` - Internal server error

## File Upload Specifications

### Supported File Types

**Images:**
- **Types**: JPEG, JPG, PNG
- **Max Size**: 1MB
- **MIME Types**: `image/jpeg`, `image/jpg`, `image/png`

**Documents:**
- **Types**: PDF, DOC, DOCX, XLS, XLSX
- **Max Size**: 5MB
- **MIME Types**:
  - `application/pdf`
  - `application/msword`
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - `application/vnd.ms-excel`
  - `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`

### File Storage

- **Directory**: Configured via `UPLOAD_DIR` environment variable
- **Structure**:
  - Images: `uploads/images/`
  - Documents: `uploads/documents/`
- **Naming**: UUID-based unique filenames to prevent conflicts
- **Access**: Direct URL access via `MEDIA_URL` configuration

### File Upload Process

1. **Validation**: File type and size validation
2. **Storage**: Save to appropriate subdirectory with unique name
3. **Database**: Store original filename and relative path
4. **Response**: Return full URL for immediate access

## Rate Limiting

### REST API
- **Default**: 100 requests per minute per user
- **File Upload**: 10 uploads per minute per user
- **Burst**: 200 requests per minute (short bursts allowed)

### WebSocket
- **Messages**: 60 messages per minute per connection
- **File Upload**: 5 file uploads per minute per connection
- **Connection**: 10 connections per minute per user

## Security Considerations

### Authentication
- **JWT Tokens**: RS256 algorithm with public/private key pairs
- **Token Expiration**: Configurable (recommended: 1 hour for access tokens)
- **Refresh Tokens**: Separate refresh token mechanism recommended

### Authorization
- **User Type Validation**: Strict validation of user types
- **Room Access Control**: Consultants own rooms, staff can join
- **Message Permissions**: Validated on every message send
- **File Access**: Role-based file access permissions

### File Security
- **Type Validation**: Strict MIME type and extension validation
- **Size Limits**: Enforced file size restrictions
- **Virus Scanning**: Recommended for production environments
- **Access Control**: File access tied to chat permissions

### WebSocket Security
- **Token Validation**: JWT validation on connection and periodically
- **Rate Limiting**: Message and connection rate limiting
- **Input Validation**: All WebSocket messages validated
- **Error Handling**: Secure error messages without sensitive data

## Examples and Testing

### cURL Examples

**Start Chat:**
```bash
curl -X POST http://localhost:8000/api/chat/start/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"consultant_id": 123, "chat_type": "consultation"}'
```

**Send Text Message:**
```bash
curl -X POST http://localhost:8000/api/chat/send/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how can I help?", "message_type": "T"}'
```

**Upload File:**
```bash
curl -X POST http://localhost:8000/api/chat/send/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "message=Please review this document" \
  -F "file=@/path/to/document.pdf" \
  -F "message_type=D"
```

**Get Chat History:**
```bash
curl -X GET "http://localhost:8000/api/chat/history/?page=1&per_page=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### WebSocket Testing

**JavaScript Example:**
```javascript
// Recommended: Get WebSocket URL from API response
const response = await fetch('/api/chat/start/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({ chat_type: 'consultation' })
});

const data = await response.json();
const ws = new WebSocket(data.data.websocket_url);

// Alternative: Manual URL construction (not recommended)
const token = 'YOUR_JWT_TOKEN';
const roomId = 'consultant_123';
const wsBaseUrl = 'ws://localhost:8080'; // This should match your environment
const ws = new WebSocket(`${wsBaseUrl}/ws/chat/room/${roomId}/?token=${token}`);

ws.onopen = function(event) {
    console.log('Connected to chat room');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};

// Send text message
ws.send(JSON.stringify({
    type: 'message',
    message: 'Hello from WebSocket!'
}));

// Send typing indicator
ws.send(JSON.stringify({
    type: 'typing',
    user_id: 123,
    user_type: 'consultant',
    is_typing: true
}));
```

## Conclusion

This API specification provides comprehensive documentation for the Chat System's REST API and WebSocket functionality. The system is designed for mobile consumption with proper authentication, authorization, and file handling capabilities.

For additional support or questions about implementation, please refer to the codebase documentation or contact the development team.
