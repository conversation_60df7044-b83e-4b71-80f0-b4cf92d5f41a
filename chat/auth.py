# -*- coding: utf-8 -*-
"""
WebSocket Authentication and Authorization for Chat System
"""
import jwt
import logging
from urllib.parse import parse_qs
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from channels.db import database_sync_to_async
from channels.middleware import BaseMiddleware

from authentication.models import TcdAppMember, TcdUserConsult
from MCDC.models import TcdUsers
from chat.models import TcdChat

logger = logging.getLogger(__name__)


class WebSocketJWTAuthMiddleware(BaseMiddleware):
    """
    Custom middleware for WebSocket JWT authentication
    Validates JWT tokens and sets user in scope
    """
    
    async def __call__(self, scope, receive, send):
        """
        Authenticate WebSocket connection using JWT token
        """
        # Only process WebSocket connections
        if scope['type'] != 'websocket':
            return await super().__call__(scope, receive, send)
        
        try:
            # Extract token from query parameters or headers
            token = await self._get_token_from_scope(scope)
            
            if not token:
                logger.warning("WebSocket connection attempted without token")
                scope['user'] = AnonymousUser()
                scope['auth_error'] = 'missing_token'
                return await super().__call__(scope, receive, send)
            
            # Validate and decode token
            user = await self._authenticate_token(token)
            
            if user:
                scope['user'] = user
                scope['auth_error'] = None
                logger.info(f"WebSocket authenticated user: {user.username} ({user.user_type})")
            else:
                scope['user'] = AnonymousUser()
                scope['auth_error'] = 'invalid_token'
                logger.warning("WebSocket authentication failed - invalid token")
                
        except Exception as e:
            logger.error(f"WebSocket authentication error: {str(e)}")
            scope['user'] = AnonymousUser()
            scope['auth_error'] = 'auth_error'
        
        return await super().__call__(scope, receive, send)
    
    async def _get_token_from_scope(self, scope):
        """
        Extract JWT token from WebSocket scope
        Supports both query parameters and headers
        """
        # Try query parameters first (most common for WebSocket)
        query_string = scope.get('query_string', b'').decode()
        if query_string:
            query_params = parse_qs(query_string)
            if 'token' in query_params:
                return query_params['token'][0]
        
        # Try headers as fallback
        headers = dict(scope.get('headers', []))
        auth_header = headers.get(b'authorization', b'').decode()
        
        if auth_header.startswith('Bearer '):
            return auth_header.replace('Bearer ', '')
        
        return None
    
    @database_sync_to_async
    def _authenticate_token(self, token):
        """
        Validate JWT token and return authenticated user
        """
        try:
            # Decode JWT token with appropriate key based on algorithm
            algorithm = settings.SIMPLE_JWT['ALGORITHM']
            if algorithm == 'RS256':
                key = settings.JWT_PUBLIC_KEY
            else:  # HS256 fallback
                key = settings.SIMPLE_JWT['VERIFYING_KEY']

            payload = jwt.decode(
                token,
                key,
                algorithms=[algorithm]
            )
            
            user_id = payload.get('user_id')
            user_type = payload.get('user_type')
            
            if not user_id or not user_type:
                logger.error("Token missing user_id or user_type")
                return None
            
            # Get user based on type
            if user_type == 'member':
                try:
                    user = TcdAppMember.objects.get(id=user_id)
                    # Check if member account is active
                    if user.status != '1':
                        logger.warning(f"Member account inactive: {user.username}")
                        return None
                    
                    # Check lockout
                    if user.lockout_end_date and user.lockout_end_date > timezone.now():
                        logger.warning(f"Member account locked: {user.username}")
                        return None
                        
                except TcdAppMember.DoesNotExist:
                    logger.error(f"Member not found: {user_id}")
                    return None
                    
            elif user_type == 'consultant':
                try:
                    user = TcdUserConsult.objects.get(id=user_id)
                except TcdUserConsult.DoesNotExist:
                    logger.error(f"Consultant not found: {user_id}")
                    return None
                    
            elif user_type == 'staff':
                try:
                    user = TcdUsers.objects.get(id=user_id)
                except TcdUsers.DoesNotExist:
                    logger.error(f"Staff user not found: {user_id}")
                    return None
            else:
                logger.error(f"Invalid user type: {user_type}")
                return None
            
            # Add authentication attributes
            user.user_type = user_type
            user.is_authenticated = True
            
            return user
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Token authentication error: {str(e)}")
            return None


def validate_websocket_chat_access(user):
    """
    Validate if user has access to chat features via WebSocket
    Same logic as REST API but for WebSocket connections

    Args:
        user: Authenticated user object

    Returns:
        tuple: (is_valid, error_message)
    """
    if not user or not user.is_authenticated:
        return False, "Authentication required"

    if not hasattr(user, 'user_type'):
        logger.error(f"User {getattr(user, 'username', 'unknown')} missing user_type attribute")
        return False, "Invalid user type"

    user_type = getattr(user, 'user_type', None)
    if user_type is None:
        logger.error(f"User {getattr(user, 'username', 'unknown')} has None user_type")
        return False, f"Invalid user_type: {user_type}"

    # Block members completely from chat features
    if user_type == 'member':
        return False, "Members are not allowed to access chat features"

    # Allow consultants and staff
    if user_type in ['consultant', 'staff']:
        return True, None

    return False, f"Unauthorized user type: {user_type}"


@database_sync_to_async
def validate_room_access(user, room_id):
    """
    Validate if user can access specific chat room

    Args:
        user: Authenticated user object
        room_id: Room identifier (format: consultation_{consultant_id})

    Returns:
        tuple: (is_valid, error_message, consultant_id)
    """
    try:
        # Parse room_id to get consultant_id
        if not room_id.startswith('consultation_'):
            return False, "Invalid room format", None

        try:
            consultant_id = int(room_id.replace('consultation_', ''))
        except ValueError:
            return False, "Invalid room ID", None

        # Verify consultant exists
        consultant_exists = TcdUserConsult.objects.filter(id=consultant_id).exists()
        if not consultant_exists:
            return False, "Consultant not found", None

        if user.user_type == 'consultant':
            # Consultants can only access their own rooms
            if user.id == consultant_id:
                return True, None, consultant_id
            else:
                return False, "Cannot access other consultant's room", None

        elif user.user_type == 'staff':
            # Staff can access any consultant's room
            return True, None, consultant_id

        return False, "Unauthorized access", None

    except Exception as e:
        logger.error(f"Room access validation error: {str(e)}")
        return False, "Validation error", None


async def validate_message_permission(user, room_id, recipient_id=None):
    """
    Validate if user can send messages in specific room

    Args:
        user: Authenticated user object
        room_id: Room identifier
        recipient_id: Optional recipient ID for targeted messages

    Returns:
        tuple: (is_valid, error_message, message_routing_info)
    """
    try:
        # First validate room access
        room_valid, room_error, consultant_id = await validate_room_access(user, room_id)
        if not room_valid:
            return False, room_error, None

        routing_info = {
            'user_consult_id': None,
            'users_id': None,
            'consultant_id': consultant_id
        }

        if user.user_type == 'consultant':
            # Consultant sending message in their own room
            routing_info['user_consult_id'] = user.id

            # If recipient_id provided, validate it's a staff member
            if recipient_id:
                @database_sync_to_async
                def check_staff_exists():
                    return TcdUsers.objects.filter(id=recipient_id).exists()

                staff_exists = await check_staff_exists()
                if not staff_exists:
                    return False, "Invalid recipient - staff member not found", None
                routing_info['users_id'] = recipient_id

        elif user.user_type == 'staff':
            # Staff responding to consultant's room
            routing_info['users_id'] = user.id
            routing_info['user_consult_id'] = consultant_id

            # Validate consultant exists (already done in room validation)

        return True, None, routing_info

    except Exception as e:
        logger.error(f"Message permission validation error: {str(e)}")
        return False, "Permission validation error", None


def get_websocket_error_response(error_type, message=None):
    """
    Generate standardized error response for WebSocket
    
    Args:
        error_type: Type of error (auth_required, access_denied, etc.)
        message: Optional custom message
        
    Returns:
        dict: Error response data
    """
    error_messages = {
        'auth_required': 'Authentication required to access chat',
        'invalid_token': 'Invalid or expired authentication token',
        'access_denied': 'Access denied to chat features',
        'room_access_denied': 'Access denied to this chat room',
        'message_permission_denied': 'Permission denied to send messages',
        'invalid_room': 'Invalid or non-existent chat room',
        'server_error': 'Internal server error'
    }
    
    return {
        'type': 'error',
        'error_type': error_type,
        'message': message or error_messages.get(error_type, 'Unknown error'),
        'timestamp': timezone.now().isoformat()
    }
