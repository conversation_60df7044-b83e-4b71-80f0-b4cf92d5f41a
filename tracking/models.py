from django.db import models


class TcdWorkHistory(models.Model):
    company_name = models.CharField(max_length=300, db_collation='Thai_CI_AI')
    position_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    position_other = models.CharField(max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    start_date = models.DateTimeField()
    stop_date = models.DateTimeField(blank=True, null=True)
    personal_general_data_id = models.DecimalField(max_digits=19, decimal_places=0)
    approver_id = models.IntegerField(blank=True, null=True)
    approver_name = models.CharField(max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)
    is_approve = models.Char<PERSON>ield(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    last_check_date = models.DateTimeField(blank=True, null=True)
    is_active = models.Char<PERSON>ield(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_work_history'


class TcdRequestType(models.Model):
    name = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_request_type'


class TcdSelectType(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_select_type'


class TcdWorklist(models.Model):
    request_type_id = models.IntegerField()
    select_type_id = models.IntegerField(blank=True, null=True)
    purpose_id = models.DecimalField(max_digits=18, decimal_places=0, blank=True, null=True)
    send_date = models.DateTimeField()
    pdmo_number = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    pdmo_date = models.DateTimeField(blank=True, null=True)
    tcd_number = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    tcd_date = models.DateTimeField(blank=True, null=True)
    name = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    project_name = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    approve_date = models.DateTimeField(blank=True, null=True)
    sign_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    status_work = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    start_date = models.DateTimeField(blank=True, null=True)
    last_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    user_consult_id = models.IntegerField(blank=True, null=True)
    send_doc = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    foreign = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    register_no = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    rating = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    sector = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    skill = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    exp_consult = models.CharField(max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    user_approve_date = models.DateTimeField(blank=True, null=True)
    ser_number = models.IntegerField(blank=True, null=True)
    ser_year = models.CharField(max_length=4, db_collation='Thai_CI_AI', blank=True, null=True)
    disapprove_date = models.DateTimeField(blank=True, null=True)
    disapprove_remark = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_worklist'


class TcdWorklistLog(models.Model):
    worklist_id = models.IntegerField()
    status_work = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    detail = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    document = models.CharField(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    expire_date = models.DateTimeField(blank=True, null=True)
    create_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_worklist_log'


class TcdWorklistUsers(models.Model):
    worklist_id = models.IntegerField()
    users_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_worklist_users'

