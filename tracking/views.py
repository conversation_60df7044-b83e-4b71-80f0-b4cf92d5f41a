from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter
from utils.response import APIResponse, get_language_from_request
from .services import WorklistService
from .serializers import (
    WorklistRequestSerializer, WorklistResponseSerializer, 
    WorklistDetailRequestSerializer, WorklistDetailResponseSerializer,
    WorklistLogRequestSerializer, WorklistLogResponseSerializer
)
import logging

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["Tracking"],
    summary="Get Worklist",
    description="ดึงรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ",
    parameters=[
        OpenApiParameter(
            name='page',
            description='หมายเลขหน้า (เริ่มต้น: 1)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
        OpenApiParameter(
            name='page_size',
            description='จำนวนรายการต่อหน้า (เริ่มต้น: 10, สูงสุด: 100)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
    ],
    responses={
        200: WorklistResponseSerializer,
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 1006},
                "error_message": {"type": "string", "example": "ไม่มีสิทธิ์เข้าถึง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 2000},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def worklist_view(request):
    """
    Get worklist data for authenticated consultant
    
    This endpoint retrieves the worklist (รายการดำเนินการ) for the authenticated consultant.
    Only consultants can access this endpoint.
    
    Query Parameters:
    - page: Page number for pagination (default: 1)
    - page_size: Number of items per page (default: 10, max: 100)
    
    Returns:
    - List of worklist items with pagination
    - Each item includes service type name, status, and formatted send date
    """
    try:
        # Get language preference
        language = get_language_from_request(request)
        
        # Validate request parameters
        serializer = WorklistRequestSerializer(data=request.GET)
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=language
            )
        
        validated_data = serializer.validated_data
        page = validated_data.get('page', 1)
        page_size = validated_data.get('page_size', 10)
        
        # Get user_consult_id from authenticated user
        user_consult_id = request.user.id
        
        # Call service to get worklist data
        result = WorklistService.get_worklist(
            user_consult_id=user_consult_id,
            page=page,
            page_size=page_size,
            language=language
        )
        
        # Check if service call was successful
        if result.get('success'):
            # Convert to standard response format
            response_data = {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": result['data']['results'],
                "page": result['data']['pagination']['page'],
                "per_page": result['data']['pagination']['page_size'],
                "total": result['data']['pagination']['total'],
                "has_next": result['data']['pagination']['has_next'],
                "api_version": "v.0.0.1"
            }
            
            from django.http import JsonResponse
            return JsonResponse(response_data, json_dumps_params={'ensure_ascii': False})
        else:
            # Service returned error
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                custom_message=result.get('error_message'),
                language=language,
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error in worklist_view: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=get_language_from_request(request),
            status_code=500
        )


@extend_schema(
    tags=["Tracking"],
    summary="Get Worklist Detail",
    description="ดึงรายละเอียดของรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ",
    parameters=[
        OpenApiParameter(
            name='worklist_id',
            description='หมายเลขรายการที่ต้องการดูรายละเอียด',
            required=True,
            type=int,
            location=OpenApiParameter.PATH
        ),
    ],
    responses={
        200: WorklistDetailResponseSerializer,
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 1006},
                "error_message": {"type": "string", "example": "ไม่มีสิทธิ์เข้าถึง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 2000},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def worklist_detail_view(request, worklist_id):
    """
    Get detailed information for a specific worklist item
    
    This endpoint retrieves detailed information about a specific worklist item
    for the authenticated consultant. Only consultants can access this endpoint
    and only for their own worklist items.
    
    Path Parameters:
    - worklist_id: ID of the worklist item to retrieve details for
    
    Returns:
    - Detailed information about the worklist item
    - Service type name based on request_type_id logic
    - Latest status from status_work field
    - All available fields from the worklist record
    """
    try:
        # Get language preference
        language = get_language_from_request(request)
        
        # Validate request parameters
        serializer = WorklistDetailRequestSerializer(data={'worklist_id': worklist_id})
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=language
            )
        
        validated_data = serializer.validated_data
        worklist_id = validated_data.get('worklist_id')
        
        # Get user_consult_id from authenticated user
        user_consult_id = request.user.id
        
        # Call service to get worklist detail data
        result = WorklistService.get_worklist_detail(
            worklist_id=worklist_id,
            user_consult_id=user_consult_id,
            language=language
        )
        
        # Check if service call was successful
        if result.get('success'):
            # Convert to standard response format
            response_data = {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": result['data'],
                "page": 1,
                "per_page": 1,
                "total": 1 if result['data'] else 0,
                "has_next": False,
                "api_version": "v.0.0.1"
            }
            
            from django.http import JsonResponse
            return JsonResponse(response_data, json_dumps_params={'ensure_ascii': False})
        else:
            # Service returned error
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                custom_message=result.get('error_message'),
                language=language,
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error in worklist_detail_view: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=get_language_from_request(request),
            status_code=500
        )


@extend_schema(
    tags=["Tracking"],
    summary="Get Worklist Logs",
    description="ดึงรายการ log สถานะของรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ",
    parameters=[
        OpenApiParameter(
            name='worklist_id',
            description='หมายเลขรายการที่ต้องการดู log สถานะ',
            required=True,
            type=int,
            location=OpenApiParameter.PATH
        ),
        OpenApiParameter(
            name='page',
            description='หมายเลขหน้า (เริ่มต้น: 1)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
        OpenApiParameter(
            name='page_size',
            description='จำนวนรายการต่อหน้า (เริ่มต้น: 10, สูงสุด: 100)',
            required=False,
            type=int,
            location=OpenApiParameter.QUERY
        ),
    ],
    responses={
        200: WorklistLogResponseSerializer,
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 1006},
                "error_message": {"type": "string", "example": "ไม่มีสิทธิ์เข้าถึง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_code": {"type": "integer", "example": 2000},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def worklist_logs_view(request, worklist_id):
    """
    Get status logs for a specific worklist item
    
    This endpoint retrieves the status logs (รายการ log สถานะ) for a specific worklist item
    for the authenticated consultant. Only consultants can access this endpoint
    and only for their own worklist items.
    
    Path Parameters:
    - worklist_id: ID of the worklist item to retrieve logs for
    
    Query Parameters:
    - page: Page number for pagination (default: 1)
    - page_size: Number of items per page (default: 10, max: 100)
    
    Returns:
    - List of status logs with pagination
    - Each log includes status name, operation date, expiry date, details, and document URL
    - Dates are formatted according to language preference (TH|EN)
    """
    try:
        # Get language preference
        language = get_language_from_request(request)
        
        # Prepare request data for validation
        request_data = {
            'worklist_id': worklist_id,
            'page': request.GET.get('page', 1),
            'page_size': request.GET.get('page_size', 10)
        }
        
        # Validate request parameters
        serializer = WorklistLogRequestSerializer(data=request_data)
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=language
            )
        
        validated_data = serializer.validated_data
        worklist_id = validated_data.get('worklist_id')
        page = validated_data.get('page', 1)
        page_size = validated_data.get('page_size', 10)
        
        # Get user_consult_id from authenticated user
        user_consult_id = request.user.id
        
        # Call service to get worklist logs data
        result = WorklistService.get_worklist_logs(
            worklist_id=worklist_id,
            user_consult_id=user_consult_id,
            page=page,
            page_size=page_size,
            language=language
        )
        
        # Check if service call was successful
        if result.get('success'):
            # Convert to standard response format
            response_data = {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": result['data']['results'],
                "page": result['data']['pagination']['page'],
                "per_page": result['data']['pagination']['page_size'],
                "total": result['data']['pagination']['total'],
                "has_next": result['data']['pagination']['has_next'],
                "api_version": "v.0.0.1"
            }
            
            from django.http import JsonResponse
            return JsonResponse(response_data, json_dumps_params={'ensure_ascii': False})
        else:
            # Service returned error
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                custom_message=result.get('error_message'),
                language=language,
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"Error in worklist_logs_view: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=get_language_from_request(request),
            status_code=500
        )
