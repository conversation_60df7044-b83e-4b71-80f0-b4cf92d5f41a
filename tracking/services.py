from django.db import models, connection
from django.core.paginator import <PERSON><PERSON>ator
from .models import TcdWorklist, TcdRequestType, TcdSelectType, TcdWorklistLog
from utils.response import service_success_response, service_error_response
import logging
from utils.i18n import th, en
from django.conf import settings

logger = logging.getLogger(__name__)


class WorklistService:
    """
    Service for handling worklist operations
    """

    @staticmethod
    def _get_status_work_name(status_work, is_log=False, language='th'):
        """
        Get status work name based on status code and language
        
        Args:
            status_work (int): Status work code
            language (str): Language preference ('th' or 'en')
            
        Returns:
            str: Status work name
        """
        try:
            if status_work is None:
                return ""
            
            if is_log:
                status_mapping = th.STATUS_WORK_LOG_MAPPING.get(status_work) if language == 'th' else en.STATUS_WORK_LOG_MAPPING.get(status_work)
            else:
                status_mapping = th.STATUS_WORK_MAPPING.get(status_work) if language == 'th' else en.STATUS_WORK_MAPPING.get(status_work)
                
            # logger.info(f"Status mapping: {status_mapping}")
            return status_mapping
        except Exception as e:
            logger.error(f"Error getting status work name: {str(e)}")
            return ""
    
    @staticmethod
    def get_worklist(user_consult_id, page=1, page_size=10, language='th'):
        """
        Get worklist data for a specific consultant using ORM
        
        Args:
            user_consult_id (int): Consultant user ID
            page (int): Page number for pagination
            page_size (int): Number of items per page
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Service response with worklist data
        """
        try:
            logger.info(f"WorklistService.get_worklist called with user_consult_id={user_consult_id}, page={page}, page_size={page_size}, language={language}")
            
            # Use ORM to get worklist data
            worklist_queryset = TcdWorklist.objects.filter(
                user_consult_id=user_consult_id,
                request_type_id__in=[1, 2, 3]
            ).order_by('-send_date')
            
            logger.info(f"ORM query executed. Found {worklist_queryset.count()} results")
            
            # Apply pagination
            paginator = Paginator(worklist_queryset, page_size)
            page_obj = paginator.get_page(page)
            
            logger.info(f"Pagination applied: total={paginator.count}, current_page={page}, items_on_page={len(page_obj)}")
            
            # Build response data
            results = []
            for item in page_obj:
                logger.debug(f"Processing item: {item}")
                
                # Get service type name based on request_type_id
                service_type_name = WorklistService._get_service_type_name(
                    item.request_type_id, item.select_type_id, language
                )
                
                result_item = {
                    # Dates
                    'id': item.id,
                    'send_date': item.send_date or None,
                    'status_work': item.status_work,
                    'status_name': WorklistService._get_status_work_name(item.status_work, is_log=False, language=language),
                    'service_type_name': service_type_name,
                }
                
                results.append(result_item)
            
            logger.info(f"Built {len(results)} result items")
            
            # Calculate pagination info
            pagination = {
                'page': page,
                'page_size': page_size,
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
            
            logger.info(f"Pagination info: {pagination}")
            
            response = service_success_response(
                data={
                    'results': results,
                    'pagination': pagination
                },
                language=language
            )
            
            logger.info("WorklistService.get_worklist completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error in WorklistService.get_worklist: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=5000,  # System error
                language=language
            )
    
    @staticmethod
    def get_worklist_detail(worklist_id, user_consult_id, language='th'):
        """
        Get detailed information for a specific worklist item using ORM
        
        Args:
            worklist_id (int): Worklist ID
            user_consult_id (int): Consultant user ID
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Service response with worklist detail data
        """
        try:
            logger.info(f"WorklistService.get_worklist_detail called with worklist_id={worklist_id}, user_consult_id={user_consult_id}, language={language}")
            
            # Use ORM to get worklist detail
            item = TcdWorklist.objects.filter(
                id=worklist_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not item:
                logger.info(f"No worklist found with id={worklist_id} and user_consult_id={user_consult_id}")
                # Return empty data instead of error
                return service_success_response(
                    data={},
                    language=language
                )
            
            logger.info(f"ORM query executed successfully. Found worklist item")
            
            # Get service type name based on request_type_id
            service_type_name = WorklistService._get_service_type_name(
                item.request_type_id, item.select_type_id, language
            )
            
            # Get status work name
            status_work_name = WorklistService._get_status_work_name(
                item.status_work, language
            )
            
            # Build detailed response data
            result = {
                # Primary fields
                'id': item.id,
                'request_type_id': item.request_type_id,
                'select_type_id': item.select_type_id,
                'purpose_id': item.purpose_id,
                'user_consult_id': item.user_consult_id,
                
                # Dates
                'send_date': item.send_date or None,
                'pdmo_date': item.pdmo_date or None,
                'tcd_date': item.tcd_date or None,
                'approve_date': item.approve_date or None,
                'sign_date': item.sign_date or None,
                'start_date': item.start_date or None,
                'last_date': item.last_date or None,
                'end_date': item.end_date or None,
                'user_approve_date': item.user_approve_date or None,
                'disapprove_date': item.disapprove_date or None,
                
                # Document numbers
                'pdmo_number': item.pdmo_number,
                'tcd_number': item.tcd_number,
                'register_no': item.register_no,
                'ser_number': item.ser_number,
                'ser_year': item.ser_year,
                
                # Project information
                'name': item.name,
                'project_name': item.project_name,
                
                # Status fields
                'status': item.status,
                'status_work': item.status_work,
                'status_name': WorklistService._get_status_work_name(item.status_work, is_log=False, language=language),
                'latest_status': item.status_work or '',  # สถานะล่าสุด
                
                # Additional fields
                'send_doc': item.send_doc,
                'foreign': item.foreign,
                'rating': item.rating,
                'sector': item.sector,
                'skill': item.skill,
                'exp_consult': item.exp_consult,
                'disapprove_remark': item.disapprove_remark,
                
                # Service type information (computed fields)
                'service_type_name': service_type_name,  # ชื่อประเภทงานขอรับบริการ
            }
            
            logger.info(f"Built detailed result item")
            
            response = service_success_response(
                data=result,
                language=language
            )
            
            logger.info("WorklistService.get_worklist_detail completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error in WorklistService.get_worklist_detail: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=5000,  # System error
                language=language
            )
    
    @staticmethod
    def get_worklist_logs(worklist_id, user_consult_id, page=1, page_size=10, language='th'):
        """
        Get worklist logs for a specific worklist item using ORM
        
        Args:
            worklist_id (int): Worklist ID
            user_consult_id (int): Consultant user ID
            page (int): Page number for pagination
            page_size (int): Number of items per page
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Service response with worklist logs data
        """
        try:
            logger.info(f"WorklistService.get_worklist_logs called with worklist_id={worklist_id}, user_consult_id={user_consult_id}, page={page}, page_size={page_size}, language={language}")
            
            # First validate that the worklist belongs to the user
            worklist_exists = TcdWorklist.objects.filter(
                id=worklist_id,
                user_consult_id=user_consult_id
            ).exists()
            
            if not worklist_exists:
                logger.warning(f"Worklist {worklist_id} does not belong to user {user_consult_id}")
                return service_error_response(
                    error_code=5000,  # Not found
                    language=language
                )
            
            # Use ORM to get worklist logs
            logs_queryset = TcdWorklistLog.objects.filter(
                worklist_id=worklist_id
            ).order_by('-id')
            
            logger.info(f"ORM query executed. Found {logs_queryset.count()} results")
            
            # Check if any results found
            if not logs_queryset.exists():
                logger.info("No worklist logs found")
                # Return empty data instead of error
                return service_success_response(
                    data={
                        'results': [],
                        'pagination': {
                            'page': page,
                            'page_size': page_size,
                            'total': 0,
                            'total_pages': 0,
                            'has_next': False,
                            'has_previous': False
                        }
                    },
                    language=language
                )
            
            # Apply pagination
            paginator = Paginator(logs_queryset, page_size)
            page_obj = paginator.get_page(page)
            
            logger.info(f"Pagination applied: total={paginator.count}, current_page={page}, items_on_page={len(page_obj)}")
            
            base_file_url = getattr(settings, 'BASE_FILE_URL', '')
            worklist_sub_dir = getattr(settings, 'WORKLIST_SUB_DIR', '')
            
            # Build response data
            results = []
            for item in page_obj:
                logger.debug(f"Processing log item: {item}")
                
                # Build file URL if document exists
                document_url = None
                if item.document:
                    document_url = base_file_url + worklist_sub_dir + item.document
                
                result_item = {
                    'id': item.id,
                    'worklist_id': item.worklist_id,
                    'status_work': item.status_work,
                    'status_name': WorklistService._get_status_work_name(item.status_work, is_log=True, language=language),
                    'create_date': item.create_date or None,
                    'expire_date': item.expire_date or None,
                    'detail': item.detail,
                    'document_url': document_url,
                }
                
                results.append(result_item)
            
            logger.info(f"Built {len(results)} result items")
            
            # Calculate pagination info
            pagination = {
                'page': page,
                'page_size': page_size,
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
            
            logger.info(f"Pagination info: {pagination}")
            
            response = service_success_response(
                data={
                    'results': results,
                    'pagination': pagination
                },
                language=language
            )
            
            logger.info("WorklistService.get_worklist_logs completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error in WorklistService.get_worklist_logs: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=5000,  # System error
                language=language
            )

    @staticmethod
    def _format_log_date(date_obj, language='th', include_time=False):
        """
        Format date for worklist logs
        
        Args:
            date_obj (datetime): Date object
            language (str): Language preference
            include_time (bool): Whether to include time in format
            
        Returns:
            str: Formatted date string
        """
        if not date_obj:
            return ""
        
        try:
            if language == 'en':
                if include_time:
                    return date_obj.strftime('%d %b %Y %H:%M')
                else:
                    return date_obj.strftime('%d %b %Y')
            else:
                # Thai month names
                thai_months = {
                    1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.',
                    5: 'พ.ค.', 6: 'มิ.ย.', 7: 'ก.ค.', 8: 'ส.ค.',
                    9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.'
                }
                day = date_obj.day
                month = thai_months.get(date_obj.month, str(date_obj.month))
                year = date_obj.year + 543  # Convert to Buddhist Era
                
                if include_time:
                    time_str = date_obj.strftime('%H:%M')
                    return f"{day} {month} {year} {time_str}"
                else:
                    return f"{day} {month} {year}"
                
        except Exception as e:
            logger.error(f"Error formatting log date: {str(e)}")
            return ""
    
    @staticmethod
    def _check_tables_exist():
        """
        Check if required tables exist in the database
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name IN ('tcd_worklist', 'request_type', 'select_type')
                """)
                count = cursor.fetchone()[0]
                return count >= 3
        except Exception as e:
            logger.error(f"Error checking tables: {str(e)}")
            return False
    
    @staticmethod
    def _get_service_type_name_from_orm(worklist_obj, language='th'):
        """
        Get service type name from ORM object
        
        Args:
            worklist_obj (TcdWorklist): Worklist object from ORM
            language (str): Language preference
            
        Returns:
            str: Service type name
        """
        try:
            request_type_id = worklist_obj.request_type_id
            
            if request_type_id == 1:
                # Use request_type table data
                if worklist_obj.request_type:
                    if language == 'en' and worklist_obj.request_type.name_en:
                        return worklist_obj.request_type.name_en
                    return worklist_obj.request_type.name
                return 'Unknown Service Type'
            else:
                # Use select_type table data
                if worklist_obj.select_type:
                    if language == 'en' and worklist_obj.select_type.name_en:
                        return worklist_obj.select_type.name_en
                    return worklist_obj.select_type.name_th
                return 'Unknown Service Type'
                
        except Exception as e:
            logger.error(f"Error getting service type name from ORM: {str(e)}")
            return "Unknown Service Type"
    
    @staticmethod
    def _get_service_type_name_from_row(row_data, language='th'):
        """
        Get service type name from raw SQL row data (kept for backward compatibility)
        
        Args:
            row_data (dict): Row data from SQL query
            language (str): Language preference
            
        Returns:
            str: Service type name
        """
        try:
            request_type_id = row_data.get('request_type_id')
            
            if request_type_id == 1:
                # Use request_type table data
                if language == 'en' and row_data.get('request_type_name_en'):
                    return row_data.get('request_type_name_en')
                return row_data.get('request_type_name', 'Unknown Service Type')
            else:
                # Use select_type table data
                if language == 'en' and row_data.get('select_type_name_en'):
                    return row_data.get('select_type_name_en')
                return row_data.get('select_type_name_th', 'Unknown Service Type')
                
        except Exception as e:
            logger.error(f"Error getting service type name from row: {str(e)}")
            return "Unknown Service Type"
    
    @staticmethod
    def _get_service_type_name(request_type_id, select_type_id, language='th'):
        """
        Get service type name based on request_type_id and select_type_id
        
        Args:
            request_type_id (int): Request type ID
            select_type_id (int): Select type ID
            language (str): Language preference
            
        Returns:
            str: Service type name
        """
        try:
            if request_type_id == 1:
                # Use request_type table
                request_type = TcdRequestType.objects.filter(id=request_type_id).first()
                if request_type:
                    if language == 'en' and request_type.name_en:
                        return request_type.name_en
                    return request_type.name
            else:
                # Use select_type table
                if select_type_id:
                    select_type = TcdSelectType.objects.filter(id=select_type_id).first()
                    if select_type:
                        if language == 'en' and select_type.name_en:
                            return select_type.name_en
                        return select_type.name_th
            
            return "Unknown Service Type"
            
        except Exception as e:
            logger.error(f"Error getting service type name: {str(e)}")
            return "Unknown Service Type"
    
    @staticmethod
    def _format_send_date(send_date, language='th'):
        """
        Format send_date to 'd MMM yyyy' format
        
        Args:
            send_date (datetime): Send date
            language (str): Language preference
            
        Returns:
            str: Formatted date string
        """
        if not send_date:
            return ""
        
        try:
            if language == 'en':
                return send_date.strftime('%d %b %Y')
            else:
                # Thai month names
                thai_months = {
                    1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.',
                    5: 'พ.ค.', 6: 'มิ.ย.', 7: 'ก.ค.', 8: 'ส.ค.',
                    9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.'
                }
                day = send_date.day
                month = thai_months.get(send_date.month, str(send_date.month))
                year = send_date.year + 543  # Convert to Buddhist Era
                return f"{day} {month} {year}"
                
        except Exception as e:
            logger.error(f"Error formatting date: {str(e)}")
            return "" 