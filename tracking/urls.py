from django.urls import path, include
from rest_framework.routers import De<PERSON>ult<PERSON>outer
from . import views

# Create router for viewsets
router = DefaultRouter()

# URL patterns
urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Worklist endpoints
    path('worklist/', views.worklist_view, name='worklist'),
    path('worklist/<int:worklist_id>/', views.worklist_detail_view, name='worklist_detail'),
    path('worklist/<int:worklist_id>/logs/', views.worklist_logs_view, name='worklist_logs'),
] 