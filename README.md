# MCDC - Medical Consultation & Document Center

A comprehensive Django REST API system for medical consultation with real-time chat, FAQ management, project management, and search functionality.

## 🚀 Features

- **Authentication System**: JWT-based authentication with RS256 algorithm
- **Real-time Chat**: WebSocket-based chat system for consultants and staff
- **FAQ Management**: Multi-language FAQ system (Thai/English)
- **Project Management**: Project tracking and management
- **Search System**: Advanced search functionality with notifications
- **File Upload**: Secure file upload system for documents and images
- **Multi-language Support**: Thai and English language support
- **API Documentation**: Comprehensive API documentation with Swagger/OpenAPI

## 📋 Table of Contents

1. [🛠️ Installation](#️-installation)
2. [⚙️ Configuration](#️-configuration)
3. [🔧 Environment Variables](#-environment-variables)
4. [🗄️ Database Setup](#️-database-setup)
5. [🔐 Authentication](#-authentication)
6. [📱 API Endpoints](#-api-endpoints)
7. [🔌 WebSocket](#-websocket)
8. [🧪 Testing](#-testing)
9. [🚀 Deployment](#-deployment)
10. [📚 API Documentation](#-api-documentation)

## 🛠️ Installation

### Prerequisites

- Python 3.8+
- SQL Server (MSSQL)
- ODBC Driver 17 for SQL Server

### 1. Clone the Repository

```bash
git clone <repository-url>
cd mcdc
```

### 2. Create Virtual Environment

```bash
# Windows
python -m venv env
.\env\Scripts\activate

# Linux/Mac
python3 -m venv env
source env/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Environment Setup

Copy the example environment file and configure it:

```bash
cp .env_example .env
```

Edit the `.env` file with your configuration (see [Environment Variables](#-environment-variables) section).

**Important**: Make sure to source the environment variables:

```bash
# Windows
source .env

# Or load manually in your terminal session
```

### 5. Generate JWT Keys

```bash
python generate_jwt_keys.py
```

### 6. Run the Server

```bash
python manage.py runserver 8000
```

The API will be available at `http://localhost:8000`

## ⚙️ Configuration

### Django Settings

The project uses environment variables for configuration. Key settings include:

- **Database**: SQL Server configuration
- **JWT**: RSA key-based authentication
- **File Upload**: Media files configuration
- **WebSocket**: Real-time communication
- **CORS**: Cross-origin resource sharing
- **Timezone**: Asia/Bangkok (GMT+7)

## 🔧 Environment Variables

Create a `.env` file in the project root with the following variables:

### Database Configuration

```bash
# Database Settings
DB_SERVICE=MCDC
DB_USER=sa
DB_PASSWORD=your_password
DB_HOST=your_host
DB_PORT=1433
```

### Application Settings

```bash
# Logging
LOG_LEVEL=ERROR

# API Key for authentication
X-API-Key=xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV

# Application URL
BASE_URL=http://localhost:8000

# Media Files
MEDIA_PREFIX=/files/
UPLOAD_DIR=uploads

# File URL Configuration
BASE_FILE_URL=http://localhost:8000/files/
CHAT_SUB_DIR=chat/
PAYMENT_SUB_DIR=payment/
```

### WebSocket Configuration

```bash
# WebSocket Settings
WEBSOCKET_HOST=localhost
WEBSOCKET_PORT=8080
WEBSOCKET_PROTOCOL=ws
```

### Email Configuration (Optional)

```bash
# SMTP Settings
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Example .env File

```bash
# Copy this content to your .env file and modify as needed

# Logging
export LOG_LEVEL=ERROR

# API Key for authentication
export X-API-Key=xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV

# Database Configuration
export DB_SERVICE=MCDC
export DB_USER=sa
export DB_PASSWORD=test12345*
export DB_HOST=************
export DB_PORT=4333

# Application Settings
export BASE_URL=************
export MEDIA_PREFIX=files/
export UPLOAD_DIR=uploads/

# File URL Configuration
export BASE_FILE_URL=http://************/files/
export CHAT_SUB_DIR=chat/
export PAYMENT_SUB_DIR=payment/

# Email Configuration
export SMTP_SERVER="smtp.gmail.com"
export SMTP_PORT=587
export SMTP_USERNAME="<EMAIL>"
export SMTP_PASSWORD=""

# WebSocket Configuration
export WEBSOCKET_HOST=************
export WEBSOCKET_PORT=8080
export WEBSOCKET_PROTOCOL=ws
```

## 🗄️ Database Setup

The project uses SQL Server (MSSQL) as the database. Make sure you have:

1. **SQL Server** installed and running
2. **ODBC Driver 17 for SQL Server** installed
3. Database named `MCDC` created
4. User with appropriate permissions

### Database Tables

The system uses the following main tables:
- `tcd_user_consult` - Consultant users
- `tcd_users` - Staff users
- `tcd_app_members` - App members (blocked from chat)
- `tcd_chat` - Chat messages
- `tcd_faq_category` - FAQ categories
- `tcd_faq` - FAQ items
- And more...

## 🔐 Authentication

The system uses JWT (JSON Web Tokens) with RS256 algorithm for authentication.

### API Key Authentication

For API access, include the API key in the header. The API key is configured in your `.env` file:

```bash
X-API-Key: xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV
```

**Note**: Make sure to set the `X-API-Key` environment variable in your `.env` file as shown in the configuration examples above.

### JWT Token Authentication

For user-specific operations, include the JWT token:

```bash
Authorization: Bearer <your_jwt_token>
```

### Generate Test Tokens

```bash
# Generate consultant token
python generate_consultant_token.py

# Generate staff token
python generate_staff_token.py
```

## 📱 API Endpoints

### Base URL
```
http://localhost:8000/api/
```

### Authentication Endpoints
- `POST /api/auth/login/` - User login
- `POST /api/auth/refresh/` - Refresh JWT token
- `POST /api/auth/logout/` - User logout

### Chat System
- `POST /api/chat/start/` - Start chat session
- `GET /api/chat/rooms/` - Get chat rooms
- `POST /api/chat/send/` - Send message
- `GET /api/chat/messages/{room_id}/` - Get chat messages

### FAQ System
- `GET /api/faq/category/` - Get FAQ categories
- `GET /api/faq/faq/` - Get FAQ items
- `GET /api/faq/faq/{id}/` - Get specific FAQ

### Project Management
- `GET /api/project/` - Get projects
- `POST /api/project/` - Create project
- `GET /api/project/{id}/` - Get specific project

### Search System
- `GET /api/search/` - Search functionality

### Response Format

All API responses follow this format:

```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": {},
    "page": 1,
    "per_page": 10,
    "total": 5,
    "has_next": false,
    "api_version": "v.0.0.1"
}
```

## 🔌 WebSocket

Real-time chat functionality using Django Channels.

### WebSocket URL
```
ws://localhost:8080/ws/chat/{room_id}/?token={jwt_token}
```

### Message Types
- `text_message` - Text messages
- `file_message` - File uploads
- `typing_start` - User started typing
- `typing_stop` - User stopped typing
- `user_joined` - User joined room
- `user_left` - User left room

## 🧪 Testing

### API Testing with cURL

#### Login Example
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -H "X-API-Key: xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

#### Get FAQ Categories
```bash
curl -X GET http://localhost:8000/api/faq/category/ \
  -H "X-API-Key: xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV"
```

#### Start Chat (with JWT token)
```bash
curl -X POST http://localhost:8000/api/chat/start/ \
  -H "Content-Type: application/json" \
  -H "X-API-Key: xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "consultant_id": 123,
    "chat_type": "consultation"
  }'
```

## 🚀 Deployment

### Production Environment Variables

```bash
# Production Database
DB_SERVICE=MCDC_PROD
DB_USER=mcdc_user
DB_PASSWORD=secure_password
DB_HOST=production_host
DB_PORT=1433

# Production URLs
BASE_URL=https://yourdomain.com
WEBSOCKET_HOST=yourdomain.com
WEBSOCKET_PORT=443
WEBSOCKET_PROTOCOL=wss

# Security
DEBUG=False
SECRET_KEY=your_production_secret_key
```

### Docker Deployment

```bash
# Build image
docker build -t mcdc .

# Run container
docker-compose up -d
```

### Server Requirements

- Python 3.8+
- SQL Server
- Redis (for production WebSocket scaling)
- Nginx (reverse proxy)
- SSL Certificate (for HTTPS/WSS)

## 📚 API Documentation

### Swagger/OpenAPI Documentation

Visit `http://localhost:8000/swagger/` for interactive API documentation.

### Available Documentation

- **Chat API**: `/chat/API_SPECIFICATION.md`
- **Chat README**: `/chat/README.md`
- **FAQ README**: `/faq/README.md`

### User Types & Permissions

1. **TcdUserConsult (Consultants)**
   - Can create and own chat rooms
   - Full access to chat features
   - Can receive messages from staff

2. **TcdUsers (Staff)**
   - Can join consultant chat rooms
   - Can send messages to consultants
   - Limited chat permissions

3. **TcdAppMembers (App Members)**
   - **BLOCKED** from all chat features
   - Read-only access to FAQ

### File Upload Limits

- **Images**: JPEG, JPG, PNG (max 1MB)
- **Documents**: PDF, DOC, DOCX, XLS, XLSX (max 5MB)

### File URL Configuration

The system uses the following settings for file URL generation:

- **BASE_FILE_URL**: Base URL for serving files (e.g., `http://localhost:8000/files/`)
- **CHAT_SUB_DIR**: Subdirectory for chat-related files (e.g., `chat/`)
- **PAYMENT_SUB_DIR**: Subdirectory for payment-related files (e.g., `payment/`)

Example file URLs:
- Chat files: `{BASE_FILE_URL}{CHAT_SUB_DIR}filename.pdf`
- Payment files: `{BASE_FILE_URL}{PAYMENT_SUB_DIR}receipt.jpg`

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check SQL Server is running
   - Verify database credentials in `.env`
   - Ensure ODBC Driver 17 is installed

2. **JWT Token Issues**
   - Generate new keys with `python generate_jwt_keys.py`
   - Check keys exist in `/keys/` folder

3. **WebSocket Connection Failed**
   - Verify WebSocket settings in `.env`
   - Check firewall settings
   - Ensure JWT token is valid

4. **File Upload Errors**
   - Check `UPLOAD_DIR` permissions
   - Verify file size limits
   - Check allowed file types

### Logs

Check logs in the `/logs/` directory for detailed error information.

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and questions, please contact the development team.

---

**MCDC API Version**: v.0.0.1  
**Last Updated**: 2024 