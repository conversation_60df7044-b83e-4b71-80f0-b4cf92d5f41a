# -*- coding: utf-8 -*-
"""
Decorators for API views
"""
from functools import wraps
from django.db import IntegrityError
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.http import Http404
from rest_framework.exceptions import ValidationError as DRFValidationError
from .response import APIResponse, get_language_from_request
import logging

logger = logging.getLogger(__name__)


def api_response_handler(view_func):
    """
    Decorator to handle API responses and exceptions
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # Get language from request
            language = get_language_from_request(request)
            
            # Execute view function
            result = view_func(request, *args, **kwargs)
            
            # If result is already a JsonResponse, return as is
            if hasattr(result, 'status_code'):
                return result
            
            # If result is a dict, wrap it in success response
            if isinstance(result, dict):
                return APIResponse.success(data=result, language=language)
            
            # If result is None or other types, return success with empty data
            return APIResponse.success(language=language)
            
        except ObjectDoesNotExist:
            logger.error(f"Object not found in {view_func.__name__}")
            return APIResponse.not_found(language=language)
            
        except Http404:
            logger.error(f"404 error in {view_func.__name__}")
            return APIResponse.not_found(language=language)
            
        except DRFValidationError as e:
            logger.error(f"DRF validation error in {view_func.__name__}: {str(e)}")
            return APIResponse.validation_error(e.detail, language=language)
            
        except ValidationError as e:
            logger.error(f"Validation error in {view_func.__name__}: {str(e)}")
            return APIResponse.validation_error(str(e), language=language)
            
        except IntegrityError as e:
            logger.error(f"Database integrity error in {view_func.__name__}: {str(e)}")
            return APIResponse.error(error_code=3000, language=language, status_code=500)
            
        except PermissionError:
            logger.error(f"Permission denied in {view_func.__name__}")
            return APIResponse.forbidden(language=language)
            
        except Exception as e:
            logger.error(f"Unexpected error in {view_func.__name__}: {str(e)}")
            return APIResponse.server_error(language=language)
    
    return wrapper


def require_authentication(view_func):
    """
    Decorator to require authentication
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Check if user is authenticated
        logger.info(f"Checking authentication for: {request.path}")
        
        if not hasattr(request, 'user'):
            logger.error(f"Authentication failed: No user attribute on request")
            language = get_language_from_request(request)
            return APIResponse.unauthorized(error_code=1000, language=language)  # Not logged in
        
        # First check for JWT authentication markers (our special attributes)
        jwt_user = getattr(request, '_jwt_authenticated_user', None)
        is_jwt_authenticated = getattr(request, '_jwt_user_authenticated', False)
        
        if is_jwt_authenticated and jwt_user:
            logger.info(f"JWT authentication found and active")
            # Replace the current user with our JWT user
            request.user = jwt_user
            # We have a JWT authenticated user, proceed with the view
            return view_func(request, *args, **kwargs)
            
        # Then check other authentication methods
        # ตรวจสอบค่า is_authenticated
        is_auth = getattr(request.user, 'is_authenticated', False)
        # If is_authenticated is a method (old Django), call it
        if callable(is_auth):
            is_auth = is_auth()
            
        # Log authentication status and user type
        user_type = getattr(request.user, 'user_type', None)
        logger.info(f"Authentication check - is_authenticated: {is_auth}")
        logger.info(f"Authentication check - user_type: {user_type}")
        
        if not is_auth:
            logger.error(f"Authentication failed: User is not authenticated")
            logger.info(f"User object: {type(request.user)}")
            logger.info(f"User dir: {dir(request.user)}")
            
            # Get auth header for debugging
            auth_header = request.META.get('HTTP_AUTHORIZATION', 'No authorization header')
            logger.info(f"Authorization header: {auth_header}")
            
            language = get_language_from_request(request)
            return APIResponse.unauthorized(error_code=1000, language=language)  # Not logged in
        
        logger.info(f"Authentication successful for user: {request.user}")
        return view_func(request, *args, **kwargs)
    
    return wrapper


def require_permissions(permissions):
    """
    Decorator to require specific permissions
    
    Args:
        permissions (list): List of required permissions
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            language = get_language_from_request(request)
            
            # Check if user is authenticated
            if not request.user.is_authenticated:
                return APIResponse.unauthorized(error_code=1000, language=language)
            
            # Check permissions
            if not all(request.user.has_perm(perm) for perm in permissions):
                return APIResponse.forbidden(error_code=1006, language=language)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_api_call(view_func):
    """
    Decorator to log API calls
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user_id = request.user.id if request.user.is_authenticated else 'Anonymous'
        logger.info(f"API Call: {view_func.__name__} by user {user_id}")
        
        result = view_func(request, *args, **kwargs)
        
        status_code = getattr(result, 'status_code', 200)
        logger.info(f"API Response: {view_func.__name__} - Status: {status_code}")
        
        return result
    
    return wrapper


# Combined decorator for common use cases
def standard_api_view(require_auth=False, permissions=None, log_calls=True):
    """
    Combined decorator for standard API views
    
    Args:
        require_auth (bool): Whether authentication is required
        permissions (list): List of required permissions
        log_calls (bool): Whether to log API calls
    """
    def decorator(view_func):
        # Apply decorators in reverse order
        func = view_func
        
        # Apply response handler first
        func = api_response_handler(func)
        
        # Apply authentication if required
        if require_auth:
            func = require_authentication(func)
        
        # Apply permissions if required
        if permissions:
            func = require_permissions(permissions)(func)
        
        # Apply logging if required
        if log_calls:
            func = log_api_call(func)
        
        return func
    
    return decorator 