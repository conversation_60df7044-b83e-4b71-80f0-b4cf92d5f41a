# -*- coding: utf-8 -*-
"""
Standard API Response Utility
"""
from django.http import JsonResponse
from django.conf import settings
from .i18n import th, en
import logging

logger = logging.getLogger(__name__)

class APIResponse:
    """
    Standard API Response class for consistent response format
    """
    API_VERSION = "v.0.0.1"
    
    @staticmethod
    def get_error_message(error_code, language='th'):
        """
        Get error message by error code and language
        
        Args:
            error_code (int): Error code
            language (str): Language code ('th' or 'en')
            
        Returns:
            str: Error message in specified language
        """
        if language == 'en':
            return en.ERROR_MESSAGES.get(error_code, "Unknown error")
        else:
            return th.ERROR_MESSAGES.get(error_code, "ข้อผิดพลาดไม่ทราบสาเหตุ")
    
    @staticmethod
    def success(data=None, language='th', status_code=200):
        """
        Create success response
        
        Args:
            data: Response data
            language (str): Language code ('th' or 'en')
            status_code (int): HTTP status code
            
        Returns:
            JsonResponse: Standard success response
        """
        response_data = {
            "status": True,
            "error_message": None,
            "error_code": None,
            "data": data if data is not None else {},
            "api_version": APIResponse.API_VERSION
        }
        
        return JsonResponse(response_data, status=status_code, json_dumps_params={'ensure_ascii': False})
    
    @staticmethod
    def error(error_code, data=None, language='th', status_code=400, custom_message=None):
        """
        Create error response
        
        Args:
            error_code (int): Error code
            data: Additional error data
            language (str): Language code ('th' or 'en')
            status_code (int): HTTP status code
            custom_message (str): Custom error message (overrides default message)
            
        Returns:
            JsonResponse: Standard error response
        """
        if custom_message:
            error_message = custom_message
        else:
            error_message = APIResponse.get_error_message(error_code, language)
        
        logger.error(f"Error code: {error_code}, Error message: {error_message}")
        
        response_data = {
            "status": False,
            "error_message": error_message,
            "error_code": error_code,
            "data": data if data is not None else {},
            "api_version": APIResponse.API_VERSION
        }
        
        return JsonResponse(response_data, status=status_code, json_dumps_params={'ensure_ascii': False})
    
    @staticmethod
    def validation_error(errors, language='th'):
        """
        Create validation error response
        
        Args:
            errors: Validation errors
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard validation error response
        """
        return APIResponse.error(
            error_code=2000,  # Invalid data provided
            data={"validation_errors": errors},
            language=language,
            status_code=400
        )
    
    @staticmethod
    def not_found(error_code=3002, language='th'):
        """
        Create not found error response
        
        Args:
            error_code (int): Error code (default: 3002 - Data not found)
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard not found error response
        """
        return APIResponse.error(
            error_code=error_code,
            language=language,
            status_code=404
        )
    
    @staticmethod
    def unauthorized(error_code=1006, language='th'):
        """
        Create unauthorized error response
        
        Args:
            error_code (int): Error code (default: 1006 - Access denied)
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard unauthorized error response
        """
        return APIResponse.error(
            error_code=error_code,
            language=language,
            status_code=401
        )
    
    @staticmethod
    def forbidden(error_code=1006, language='th'):
        """
        Create forbidden error response
        
        Args:
            error_code (int): Error code (default: 1006 - Access denied)
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard forbidden error response
        """
        return APIResponse.error(
            error_code=error_code,
            language=language,
            status_code=403
        )
    
    @staticmethod
    def server_error(error_code=5000, language='th'):
        """
        Create server error response
        
        Args:
            error_code (int): Error code (default: 5000 - System error)
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard server error response
        """
        return APIResponse.error(
            error_code=error_code,
            language=language,
            status_code=500
        )

    @staticmethod
    def smart_validation_error(errors, language='th'):
        """
        Create validation error response with smart error code detection
        
        Args:
            errors: Validation errors from serializer
            language (str): Language code ('th' or 'en')
            
        Returns:
            JsonResponse: Standard validation error response with appropriate error code
        """
        # ตรวจสอบ error ที่เฉพาะเจาะจงเพื่อเลือก error code ที่เหมาะสม
        if isinstance(errors, dict):
            if 'otp_token' in errors:
                error_code = 2025  # OTP token invalid
            elif 'username' in errors:
                error_code = 2013  # Username already exists
            elif 'email' in errors:
                error_code = 2011  # Email already exists
            elif 'phone' in errors:
                error_code = 2012  # Phone already exists
            elif 'identity_card_no' in errors:
                error_code = 2007  # Data duplicate
            elif any(field in errors for field in ['first_name', 'last_name', 'password']):
                error_code = 2001  # Required field missing
            else:
                error_code = 2000  # Invalid data provided
        else:
            error_code = 2000  # Invalid data provided
        
        return APIResponse.error(
            error_code=error_code,
            data={"validation_errors": errors},
            language=language,
            status_code=400
        )


class LanguageMiddleware:
    """
    Middleware to detect language from request headers
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Get language from headers, default to 'th'
        language = request.META.get('HTTP_ACCEPT_LANGUAGE', 'th')
        
        # Parse language header (simple parsing)
        if 'en' in language.lower():
            request.language = 'en'
        else:
            request.language = 'th'
        
        response = self.get_response(request)
        return response


def get_language_from_request(request):
    """
    Helper function to get language from request
    
    Args:
        request: Django request object
        
    Returns:
        str: Language code ('th' or 'en')
    """
    # Check if language is already set by middleware
    if hasattr(request, 'language'):
        return request.language
    
    # Check query parameter
    lang = request.GET.get('lang', '').lower()
    if lang in ['th', 'en']:
        return lang
    
    # Check headers
    accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
    if 'en' in accept_language.lower():
        return 'en'
    
    # Default to Thai
    return 'th'


# Service Response Utilities
# ------------------------------

def service_success_response(data=None, language='th'):
    """
    สร้าง success response สำหรับ service
    
    Args:
        data: ข้อมูลที่ต้องการส่งกลับ
        language: ภาษา
        
    Returns:
        dict: Standard response format
    """
    response_data = {
        'success': True,
        'data': data if data is not None else {},
    }
    return response_data

def service_error_response(error_code, language='th'):
    """
    สร้าง error response สำหรับ service
    
    Args:
        error_code: Standard error code
        language: ภาษา
        
    Returns:
        dict: Standard error response format
    """
    # Get error message from standard messages
    if language == 'en':
        error_message = en.ERROR_MESSAGES.get(error_code, "Unknown error")
    else:
        error_message = th.ERROR_MESSAGES.get(error_code, "ข้อผิดพลาดไม่ทราบสาเหตุ")
    
    response_data = {
        'success': False,
        'error_code': error_code,
        'error_message': error_message,
        'data': {},
    }
    return response_data 