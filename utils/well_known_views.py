import os
import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings


@csrf_exempt
@require_http_methods(["GET"])
def apple_app_site_association(request):
    """
    Serve apple-app-site-association file for iOS Universal Links and Web Credentials
    This endpoint is accessible without authentication
    """
    try:
        # Path to the apple-app-site-association file
        file_path = os.path.join(settings.BASE_DIR, 'utils', 'well_know', 'apple-app-site-association')
        
        # Check if file exists
        if not os.path.exists(file_path):
            return JsonResponse(
                {"error": "apple-app-site-association file not found"},
                status=404,
                content_type='application/json'
            )
        
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Parse JSON to validate it's valid
        json_data = json.loads(content)
        
        # Return the JSON response with proper content type
        response = JsonResponse(json_data, content_type='application/json')
        
        # Set cache headers for better performance
        response['Cache-Control'] = 'public, max-age=3600'  # Cache for 1 hour
        
        return response
        
    except json.JSONDecodeError as e:
        return JsonResponse(
            {"error": f"Invalid JSON in apple-app-site-association file: {str(e)}"},
            status=500,
            content_type='application/json'
        )
    except Exception as e:
        return JsonResponse(
            {"error": f"Error serving apple-app-site-association: {str(e)}"},
            status=500,
            content_type='application/json'
        )


@csrf_exempt
@require_http_methods(["GET"])
def android_app_site_association(request):
    """
    Serve assetlinks.json file for Android App Links
    This endpoint is accessible without authentication
    """
    try:
        # Path to the assetlinks.json file
        file_path = os.path.join(settings.BASE_DIR, 'utils', 'well_know', 'assetlinks.json')
        
        # Check if file exists
        if not os.path.exists(file_path):
            return JsonResponse(
                {"error": "assetlinks.json file not found"},
                status=404,
                content_type='application/json'
            )
        
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Parse JSON to validate it's valid
        json_data = json.loads(content)
        
        # Return the JSON response with proper content type
        # Use safe=False to allow non-dict objects (like lists) to be serialized
        response = JsonResponse(json_data, content_type='application/json', safe=False)
        
        # Set cache headers for better performance
        response['Cache-Control'] = 'public, max-age=3600'  # Cache for 1 hour
        
        return response
        
    except json.JSONDecodeError as e:
        return JsonResponse(
            {"error": f"Invalid JSON in assetlinks.json file: {str(e)}"},
            status=500,
            content_type='application/json'
        )
    except Exception as e:
        return JsonResponse(
            {"error": f"Error serving assetlinks.json: {str(e)}"},
            status=500,
            content_type='application/json'
        )

