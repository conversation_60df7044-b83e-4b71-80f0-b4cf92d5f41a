from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for ViewSet
router = DefaultRouter()
router.register(r'news', views.NewsViewSet, basename='news')


urlpatterns = [
    # Dashboard Statistics Endpoints
    path('dashboard/overview/', views.dashboard_overview, name='dashboard_overview'),
    path('dashboard/category/', views.dashboard_category, name='dashboard_category'),
    path('dashboard/list/', views.dashboard_list, name='dashboard_list'),
    
    # ViewSet routes
    path('', include(router.urls)),
] 