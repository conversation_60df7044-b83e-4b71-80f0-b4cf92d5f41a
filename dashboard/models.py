from django.db import models


class TcdDashboard(models.Model):
    dashboard_category_id = models.IntegerField()
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    embed_url = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    mobile_embed_url = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    thumbnail = models.CharField(max_length=100, db_collation='Thai_CI_AI')
    is_consult = models.BooleanField()
    is_matching = models.BooleanField()
    is_application = models.BooleanField()
    status = models.BooleanField()
    create_user_id = models.IntegerField()
    create_date = models.DateTimeField()
    update_user_id = models.IntegerField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_dashboard'


class TcdDashboardCategory(models.Model):
    name_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    order = models.IntegerField()
    status = models.BooleanField()
    create_user_id = models.IntegerField()
    create_date = models.DateTimeField()
    update_user_id = models.IntegerField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_dashboard_category'

