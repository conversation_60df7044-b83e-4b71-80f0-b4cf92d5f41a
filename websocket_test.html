<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced WebSocket Chat Test with File Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .error {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        .auth-section {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .upload-section {
            background: #f3e5f5;
            border: 1px solid #ce93d8;
        }
        .section-title {
            color: #495057;
            font-weight: bold;
            font-size: 18px;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .message {
            margin: 8px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            word-wrap: break-word;
        }
        .message.error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .message.success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        .message.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }
        .message.file {
            border-left-color: #6f42c1;
            background: #f3e5f5;
            color: #6f42c1;
        }
        .controls {
            margin: 15px 0;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 12px 0;
            flex-wrap: wrap;
        }
        input, button, select, textarea {
            padding: 10px;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #218838;
        }
        #messageInput {
            width: 300px;
            min-width: 250px;
        }
        #jwtToken {
            width: 450px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        label {
            font-weight: bold;
            margin-right: 10px;
            color: #495057;
        }

        /* File Upload Styles */
        .message-type-selector {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .message-type-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .message-type-option:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .message-type-option.active {
            border-color: #007bff;
            background: #e3f2fd;
            color: #007bff;
        }
        .message-type-option input[type="radio"] {
            margin: 0;
            padding: 0;
        }

        .file-upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 15px 0;
            background: #fafafa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .file-upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .file-upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
        }
        .file-upload-area.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .file-input {
            display: none;
        }

        .file-preview {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .file-preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .file-icon {
            font-size: 24px;
        }
        .file-details {
            flex: 1;
        }
        .file-name {
            font-weight: bold;
            color: #495057;
        }
        .file-size {
            color: #6c757d;
            font-size: 12px;
        }
        .file-remove {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .validation-message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-size: 14px;
        }
        .validation-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .validation-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            #messageInput, #jwtToken {
                width: 100%;
                min-width: auto;
            }
            .message-type-selector {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Enhanced WebSocket Chat Test with File Upload</h1>

        <div id="status" class="status disconnected">
            ❌ Disconnected - Authentication Required
        </div>

        <!-- Authentication Section -->
        <div class="section auth-section">
            <div class="section-title">🔑 Connection Settings</div>

            <div class="control-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="ws://localhost:8080/ws/chat/room/consultation_1/" style="width: 450px;">
                <small style="display: block; color: #666; margin-top: 5px;">
                    💡 URL is configurable via environment variables: WEBSOCKET_HOST, WEBSOCKET_PORT, WEBSOCKET_PROTOCOL
                </small>
            </div>

            <div class="control-group">
                <label for="jwtToken">JWT Token:</label>
                <input type="text" id="jwtToken" placeholder="Paste your JWT token here">
            </div>

            <div class="control-group">
                <button onclick="connect()" id="connectBtn">🔗 Connect</button>
                <button onclick="disconnect()" id="disconnectBtn" disabled>🔌 Disconnect</button>
                <button onclick="clearMessages()" class="danger">🗑️ Clear Messages</button>
            </div>
        </div>

        <!-- Message Type Selection -->
        <div class="section upload-section">
            <div class="section-title">💬 Send Message</div>

            <div class="section-title" style="font-size: 16px; margin-bottom: 10px;">📝 Choose Message Type (Select ONE):</div>
            <div class="message-type-selector">
                <div class="message-type-option active" onclick="selectMessageType('text')">
                    <input type="radio" name="messageType" value="text" checked>
                    <span>💬 Text Message Only</span>
                </div>
                <div class="message-type-option" onclick="selectMessageType('image')">
                    <input type="radio" name="messageType" value="image">
                    <span>🖼️ Image Upload Only</span>
                </div>
                <div class="message-type-option" onclick="selectMessageType('document')">
                    <input type="radio" name="messageType" value="document">
                    <span>📄 Document/File Upload Only</span>
                </div>
            </div>

            <!-- Text Message Input -->
            <div id="textMessageSection" class="message-input-section">
                <div class="control-group">
                    <input type="text" id="messageInput" placeholder="Type your message here...">
                    <button onclick="sendTextMessage()" id="sendTextBtn" disabled class="success">📨 Send Text</button>
                </div>
            </div>

            <!-- File Upload Section -->
            <div id="fileUploadSection" class="message-input-section" style="display: none;">
                <div class="file-upload-area" onclick="triggerFileInput()" ondrop="handleFileDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div id="uploadAreaContent">
                        <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">Click to select file or drag & drop</div>
                        <div id="fileConstraints" style="font-size: 14px; color: #6c757d;">
                            <div>📸 Images: JPEG, JPG, PNG (Max: 1MB)</div>
                            <div>📄 Documents: PDF, DOC, DOCX, XLS, XLSX (Max: 5MB)</div>
                        </div>
                    </div>
                </div>

                <input type="file" id="fileInput" class="file-input" onchange="handleFileSelect(event)" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx">

                <div id="filePreview" class="file-preview" style="display: none;">
                    <!-- File preview will be inserted here -->
                </div>

                <div id="validationMessage" class="validation-message" style="display: none;">
                    <!-- Validation messages will be shown here -->
                </div>

                <div class="control-group">
                    <button onclick="sendFileMessage()" id="sendFileBtn" disabled class="success">📤 Send File</button>
                    <button onclick="clearFileSelection()" id="clearFileBtn" disabled class="danger">🗑️ Clear File</button>
                </div>
            </div>
        </div>

        <!-- Messages Display -->
        <div class="section">
            <div class="section-title">📋 Messages & Connection Log</div>
            <div id="messages" class="messages">
                <div class="message">📋 Enhanced WebSocket Chat Test Ready...</div>
                <div class="message success">🎯 Features: Text messages, Image upload, Document upload</div>
                <div class="message warning">⚠️ File Constraints: Images (1MB), Documents (5MB)</div>
            </div>
        </div>
    </div>

    <script>
        let socket = null;
        let isConnected = false;
        let isAuthenticated = false;
        let selectedFile = null;
        let currentMessageType = 'text';

        // File validation constraints based on Thai requirements
        const FILE_CONSTRAINTS = {
            image: {
                types: ['image/jpeg', 'image/jpg', 'image/png'],
                extensions: ['.jpg', '.jpeg', '.png'],
                maxSize: 1 * 1024 * 1024, // 1MB
                label: 'Images'
            },
            document: {
                types: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
                extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx'],
                maxSize: 5 * 1024 * 1024, // 5MB
                label: 'Documents'
            }
        };

        function addMessage(message, type = 'info') {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = status;
            statusElement.className = `status ${className}`;
        }

        function updateButtonStates() {
            const connected = socket && socket.readyState === WebSocket.OPEN;
            const hasToken = document.getElementById('jwtToken').value.trim() !== '';
            const hasFile = selectedFile !== null;
            const hasTextMessage = document.getElementById('messageInput').value.trim() !== '';

            document.getElementById('connectBtn').disabled = connected || !hasToken;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('sendTextBtn').disabled = !connected || !isAuthenticated || !hasTextMessage;
            document.getElementById('sendFileBtn').disabled = !connected || !isAuthenticated || !hasFile;
            document.getElementById('clearFileBtn').disabled = !hasFile;
        }

        // Message Type Selection Functions
        function selectMessageType(type) {
            currentMessageType = type;

            // Update UI
            document.querySelectorAll('.message-type-option').forEach(option => {
                option.classList.remove('active');
            });
            event.target.closest('.message-type-option').classList.add('active');

            // Update radio button
            document.querySelector(`input[value="${type}"]`).checked = true;

            // Show/hide appropriate sections
            const textSection = document.getElementById('textMessageSection');
            const fileSection = document.getElementById('fileUploadSection');

            if (type === 'text') {
                textSection.style.display = 'block';
                fileSection.style.display = 'none';
                clearFileSelection();
            } else {
                textSection.style.display = 'none';
                fileSection.style.display = 'block';
                document.getElementById('messageInput').value = '';
            }

            // Update file constraints display
            updateFileConstraintsDisplay();
            updateButtonStates();
        }

        function updateFileConstraintsDisplay() {
            const constraintsDiv = document.getElementById('fileConstraints');
            if (currentMessageType === 'image') {
                constraintsDiv.innerHTML = '<div>📸 Images: JPEG, JPG, PNG (Max: 1MB)</div>';
            } else if (currentMessageType === 'document') {
                constraintsDiv.innerHTML = '<div>📄 Documents: PDF, DOC, DOCX, XLS, XLSX (Max: 5MB)</div>';
            } else {
                constraintsDiv.innerHTML = `
                    <div>📸 Images: JPEG, JPG, PNG (Max: 1MB)</div>
                    <div>📄 Documents: PDF, DOC, DOCX, XLS, XLSX (Max: 5MB)</div>
                `;
            }
        }

        function connect() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                addMessage('⚠️ Already connected!', 'warning');
                return;
            }

            const wsUrl = document.getElementById('wsUrl').value;
            const token = document.getElementById('jwtToken').value;

            if (!token) {
                addMessage('❌ Error: JWT token is required', 'error');
                updateStatus('Error: No token provided', 'error');
                return;
            }

            // Add token as query parameter
            const urlWithToken = `${wsUrl}?token=${encodeURIComponent(token)}`;

            addMessage(`🔗 Connecting to: ${urlWithToken}`);
            updateStatus('🔄 Connecting...', 'error');

            try {
                socket = new WebSocket(urlWithToken);

                socket.onopen = function(event) {
                    addMessage('✅ WebSocket connection opened successfully!', 'success');
                    updateStatus('🔄 Connected - Waiting for authentication...', 'connected');
                    isConnected = true;
                    addMessage('⏳ Waiting for authentication response...');
                    updateButtonStates();
                };

                socket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (e) {
                        addMessage(`📨 Received (raw): ${event.data}`, 'info');
                    }
                };

                socket.onclose = function(event) {
                    isAuthenticated = false;
                    isConnected = false;
                    updateStatus('🔌 Disconnected', 'disconnected');
                    addMessage(`🔌 WebSocket Closed: Code ${event.code}, Reason: ${event.reason || 'No reason'}`, 'warning');

                    // Explain common error codes with authentication context
                    if (event.code === 4001) {
                        addMessage('🔐 Error 4001: Unauthorized - Authentication required', 'error');
                        addMessage('   • Missing or invalid JWT token', 'error');
                        addMessage('   • Token expired or malformed', 'error');
                    } else if (event.code === 4003) {
                        addMessage('🚫 Error 4003: Forbidden - Access denied', 'error');
                        addMessage('   • User type not allowed (e.g., members blocked)', 'error');
                        addMessage('   • No permission to access this room', 'error');
                    } else if (event.code === 1006) {
                        addMessage('💡 Error 1006: Abnormal closure (server rejected connection)', 'error');
                        addMessage('   • Check if Django server is running with ASGI', 'error');
                        addMessage('   • Verify WebSocket URL and authentication', 'error');
                        addMessage('   • Check server logs for detailed errors', 'error');
                    } else if (event.code === 1000) {
                        addMessage('✅ Normal closure', 'success');
                    }

                    updateButtonStates();
                };

                socket.onerror = function(error) {
                    addMessage(`❌ WebSocket Error: ${error}`, 'error');
                    updateStatus('❌ Connection Error', 'error');
                    addMessage('🔍 Common causes:', 'error');
                    addMessage('   • Django server not running with ASGI/Channels', 'error');
                    addMessage('   • Invalid or expired JWT token', 'error');
                    addMessage('   • Authentication/authorization failure', 'error');
                    addMessage('   • Wrong WebSocket URL or room ID', 'error');
                    addMessage('   • Network/firewall blocking connection', 'error');
                    isAuthenticated = false;
                    updateButtonStates();
                };

            } catch (error) {
                addMessage(`❌ Connection error: ${error.message}`, 'error');
                updateStatus('Connection Error', 'error');
            }
        }

        function handleWebSocketMessage(data) {
            addMessage(`📨 Received: ${JSON.stringify(data, null, 2)}`, 'info');

            // Handle specific message types
            if (data.type === 'connection_established') {
                isAuthenticated = true;
                updateStatus(`✅ Authenticated as ${data.user_type}`, 'connected');
                addMessage(`🎉 Successfully authenticated as ${data.user_type}!`, 'success');
                addMessage(`🏠 Connected to room: ${data.room_id}`, 'success');
                updateButtonStates();
            } else if (data.type === 'error') {
                isAuthenticated = false;
                updateStatus(`❌ ${data.error_type}`, 'error');
                addMessage(`❌ Error: ${data.error_type} - ${data.message}`, 'error');
                updateButtonStates();
            } else if (data.type === 'message') {
                if (data.data?.file_name) {
                    addMessage(`📎 File Message: ${data.data.file_name} - ${data.data?.message || 'No message content'}`, 'file');
                } else {
                    addMessage(`💬 Chat Message: ${data.data?.message || 'No message content'}`, 'success');
                }
            } else if (data.type === 'typing') {
                addMessage(`⌨️ Typing indicator from user ${data.user_id}`, 'info');
            } else if (data.type === 'user_joined') {
                addMessage(`👋 User ${data.user_id} (${data.user_type}) joined the room`, 'info');
            }
        }

        function disconnect() {
            if (socket) {
                socket.close(1000, 'User requested disconnect');
                socket = null;
                addMessage('🔌 Disconnecting...', 'info');
                isAuthenticated = false;
                isConnected = false;
                updateButtonStates();
            } else {
                addMessage('⚠️ No connection to close', 'warning');
            }
        }

        // File Upload Functions
        function triggerFileInput() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                validateAndPreviewFile(file);
            }
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.stopPropagation();

            const uploadArea = document.querySelector('.file-upload-area');
            uploadArea.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                validateAndPreviewFile(files[0]);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            document.querySelector('.file-upload-area').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            document.querySelector('.file-upload-area').classList.remove('dragover');
        }

        function validateAndPreviewFile(file) {
            const validationResult = validateFile(file);

            if (!validationResult.valid) {
                showValidationMessage(validationResult.message, 'error');
                clearFileSelection();
                return;
            }

            selectedFile = file;
            showFilePreview(file);
            showValidationMessage('✅ File selected successfully!', 'success');
            updateButtonStates();
        }

        function validateFile(file) {
            const fileType = currentMessageType === 'text' ?
                (file.type.startsWith('image/') ? 'image' : 'document') :
                currentMessageType;

            const constraints = FILE_CONSTRAINTS[fileType];

            if (!constraints) {
                return { valid: false, message: 'Invalid file type for current message type' };
            }

            // Check file type
            const isValidType = constraints.types.some(type => file.type === type) ||
                               constraints.extensions.some(ext => file.name.toLowerCase().endsWith(ext));

            if (!isValidType) {
                return {
                    valid: false,
                    message: `❌ Invalid file type. Allowed: ${constraints.extensions.join(', ')}`
                };
            }

            // Check file size
            if (file.size > constraints.maxSize) {
                const maxSizeMB = constraints.maxSize / (1024 * 1024);
                return {
                    valid: false,
                    message: `❌ File too large. Maximum size: ${maxSizeMB}MB`
                };
            }

            return { valid: true, message: 'File is valid' };
        }

        function showFilePreview(file) {
            const preview = document.getElementById('filePreview');
            const fileType = file.type.startsWith('image/') ? 'image' : 'document';

            let previewHTML = `
                <div class="file-info">
                    <div class="file-icon">${fileType === 'image' ? '🖼️' : '📄'}</div>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <button class="file-remove" onclick="clearFileSelection()">❌ Remove</button>
                </div>
            `;

            if (fileType === 'image') {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewHTML += `<img src="${e.target.result}" alt="Preview">`;
                    preview.innerHTML = previewHTML;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = previewHTML;
            }

            preview.style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showValidationMessage(message, type) {
            const validationDiv = document.getElementById('validationMessage');
            validationDiv.className = `validation-message ${type}`;
            validationDiv.textContent = message;
            validationDiv.style.display = 'block';

            // Auto-hide success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    validationDiv.style.display = 'none';
                }, 3000);
            }
        }

        function clearFileSelection() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('filePreview').style.display = 'none';
            document.getElementById('validationMessage').style.display = 'none';
            document.querySelector('.file-upload-area').classList.remove('error');
            updateButtonStates();
        }

        // Message Sending Functions
        function sendTextMessage() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('❌ Not connected! Please connect first.', 'error');
                return;
            }

            if (!isAuthenticated) {
                addMessage('❌ Not authenticated! Please wait for authentication confirmation.', 'error');
                return;
            }

            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) {
                addMessage('⚠️ Please enter a message', 'warning');
                return;
            }

            const messageData = {
                type: 'message',
                message: message,
                timestamp: new Date().toISOString()
            };

            try {
                socket.send(JSON.stringify(messageData));
                addMessage(`📤 Sent text: ${message}`, 'success');
                messageInput.value = '';
                updateButtonStates();
            } catch (error) {
                addMessage(`❌ Send error: ${error.message}`, 'error');
            }
        }
        function sendFileMessage() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('❌ Not connected! Please connect first.', 'error');
                return;
            }

            if (!isAuthenticated) {
                addMessage('❌ Not authenticated! Please wait for authentication confirmation.', 'error');
                return;
            }

            if (!selectedFile) {
                addMessage('❌ No file selected!', 'error');
                return;
            }

            addMessage(`📤 Preparing to send file: ${selectedFile.name}...`, 'info');

            // Read file as base64
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // Get base64 data (remove data URL prefix if present)
                    let base64Data = e.target.result;
                    if (base64Data.includes(',')) {
                        base64Data = base64Data.split(',')[1];
                    }

                    // Determine message type based on file extension (matching consumer logic)
                    const fileName = selectedFile.name.toLowerCase();
                    const fileExtension = fileName.split('.').pop();

                    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                    const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'rtf'];

                    let expectedMessageType = 'F'; // Default to generic file
                    if (imageExtensions.includes(fileExtension)) {
                        expectedMessageType = 'I'; // Image
                    } else if (documentExtensions.includes(fileExtension)) {
                        expectedMessageType = 'D'; // Document
                    }

                    // Create file data in the format expected by the WebSocket consumer
                    const fileData = {
                        name: selectedFile.name,
                        type: selectedFile.type,
                        data: base64Data  // This is the base64 encoded file content
                    };

                    // Send message in the format expected by the WebSocket consumer
                    const messageData = {
                        type: 'message',
                        message: `File: ${selectedFile.name}`,
                        file_data: fileData  // This is what the consumer expects
                    };

                    addMessage(`📤 Sending file with ${base64Data.length} characters of base64 data...`, 'info');

                    socket.send(JSON.stringify(messageData));
                    addMessage(`📤 Sent file: ${selectedFile.name} (${formatFileSize(selectedFile.size)})`, 'file');
                    addMessage(`📎 Expected file type: ${expectedMessageType === 'I' ? 'Image' : expectedMessageType === 'D' ? 'Document' : 'Generic File'}`, 'info');
                    clearFileSelection();

                } catch (error) {
                    addMessage(`❌ Send error: ${error.message}`, 'error');
                }
            };

            reader.onerror = function(error) {
                addMessage(`❌ File read error: ${error}`, 'error');
            };

            // Read file as data URL (base64)
            reader.readAsDataURL(selectedFile);
        }

        function clearMessages() {
            const messages = document.getElementById('messages');
            messages.innerHTML = '<div class="message">📋 Messages cleared...</div>';
        }

        // Event Listeners and Initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Allow Enter key to send text message
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTextMessage();
                }
            });

            // Update button states on input change
            document.getElementById('messageInput').addEventListener('input', updateButtonStates);
            document.getElementById('jwtToken').addEventListener('input', updateButtonStates);

            // Initialize UI
            updateButtonStates();
            updateFileConstraintsDisplay();

            // Initial messages
            addMessage('🚀 Enhanced WebSocket Chat Test Ready', 'success');
            addMessage('📋 Instructions:', 'info');
            addMessage('   1. Enter your JWT token', 'info');
            addMessage('   2. Click "Connect" to establish secure connection', 'info');
            addMessage('   3. Choose message type: Text, Image, or Document', 'info');
            addMessage('   4. Send messages after authentication succeeds', 'info');
            addMessage('⚠️ File Constraints: Images (1MB max), Documents (5MB max)', 'warning');
        });
    </script>
</body>
</html>
