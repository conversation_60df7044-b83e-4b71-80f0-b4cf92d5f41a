from rest_framework import serializers
from typing import Dict, Any


class SatisfactionSurveyMenuDisplaySerializer(serializers.Serializer):
    """
    Serializer for satisfaction survey menu display check
    """
    question_position_id = serializers.IntegerField(
        default=5,
        help_text="Question position ID to check (default: 5)"
    )


class SatisfactionSurveyMenuDisplayResponseSerializer(serializers.Serializer):
    """
    Serializer for satisfaction survey menu display response
    """
    question_position_id = serializers.IntegerField()
    should_display = serializers.BooleanField()
    row_count = serializers.IntegerField()
    questionnaire_id = serializers.IntegerField()
    questionnaire_name = serializers.Char<PERSON>ield()


class SurveysResponseSerializer(serializers.Serializer):
    """
    Standard response serializer for surveys API
    """
    success = serializers.BooleanField()
    error_code = serializers.CharField(allow_null=True, allow_blank=True)
    error_message = serializers.CharField(allow_null=True, allow_blank=True)
    data = serializers.DictField()
    api_version = serializers.CharField()


class QuestionnaireSerializer(serializers.Serializer):
    """
    Serializer for questionnaire data
    """
    id = serializers.IntegerField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    count = serializers.IntegerField()
    createdate = serializers.DateTimeField()
    createuser = serializers.IntegerField()
    status = serializers.CharField()


class ActiveQuestionnairesResponseSerializer(serializers.Serializer):
    """
    Serializer for active questionnaires response
    """
    questionnaires = QuestionnaireSerializer(many=True)
    total = serializers.IntegerField()


class QuestionnaireQuestionSerializer(serializers.Serializer):
    """
    Serializer for questionnaire question data
    """
    id = serializers.IntegerField()
    question_th = serializers.CharField()
    question_en = serializers.CharField()
    order = serializers.IntegerField()


class QuestionnaireQuestionsResponseSerializer(serializers.Serializer):
    """
    Serializer for questionnaire questions response
    """
    questionnaire_id = serializers.IntegerField()
    questions = QuestionnaireQuestionSerializer(many=True)
    total = serializers.IntegerField() 