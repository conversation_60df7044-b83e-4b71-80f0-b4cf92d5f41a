from django.urls import path, include
from . import views
from rest_framework.routers import DefaultRouter

app_name = 'faq'

# Create router for FAQ Category
faq_category_router = DefaultRouter()
faq_category_router.register("", views.TcdAppFaqcategoryViewSet, basename="faq-category")

# Create router for FAQ
faq_router = DefaultRouter()
faq_router.register("", views.TcdAppFaqViewSet, basename="faq")

urlpatterns = [
    # FAQ Category routes
    path("category/", include(faq_category_router.urls)),
    
    # FAQ routes
    path("", include(faq_router.urls)),
]
