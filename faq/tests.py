from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import Mock, patch
from django.utils import timezone

from .models import TcdAppFaqcategory, TcdAppFaq


class TcdAppFaqcategoryViewSetTestCase(APITestCase):
    """
    Test cases for FAQ Category ViewSet
    """

    def setUp(self):
        """Set up test data"""
        self.list_url = reverse('faq:faq-category-list')

    @patch('faq.views.TcdAppFaqcategory.objects')
    def test_category_list_success(self, mock_category_objects):
        """Test successful FAQ category list retrieval"""
        # Mock category data
        mock_category = Mock()
        mock_category.id = 1
        mock_category.name_th = 'หมวดหมู่ทดสอบ'
        mock_category.name_en = 'Test Category'
        mock_category.order = 1

        mock_queryset = Mock()
        mock_queryset.order_by.return_value = [mock_category]
        mock_category_objects.all.return_value = mock_queryset

        # Make request
        response = self.client.get(self.list_url)

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_category_detail_url_pattern(self):
        """Test category detail URL pattern"""
        detail_url = reverse('faq:faq-category-detail', kwargs={'pk': 1})
        self.assertIn('/api/faq/category/1/', detail_url)


class TcdAppFaqViewSetTestCase(APITestCase):
    """
    Test cases for FAQ ViewSet
    """

    def setUp(self):
        """Set up test data"""
        self.list_url = reverse('faq:faq-list')

    @patch('faq.views.TcdAppFaq.objects')
    def test_faq_list_success(self, mock_faq_objects):
        """Test successful FAQ list retrieval"""
        # Mock FAQ data
        mock_faq = Mock()
        mock_faq.id = 1
        mock_faq.question_th = 'คำถามทดสอบ'
        mock_faq.question_en = 'Test Question'
        mock_faq.answer_th = 'คำตอบทดสอบ'
        mock_faq.answer_en = 'Test Answer'
        mock_faq.status = True
        mock_faq.is_app = True
        mock_faq.is_web = True

        # Mock category
        mock_category = Mock()
        mock_category.name_th = 'หมวดหมู่ทดสอบ'
        mock_category.name_en = 'Test Category'
        mock_faq.app_faqcategory = mock_category

        mock_queryset = Mock()
        mock_queryset.select_related.return_value.filter.return_value.order_by.return_value = [mock_faq]
        mock_faq_objects.select_related.return_value = mock_queryset

        # Make request
        response = self.client.get(self.list_url)

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_faq_list_with_category_filter(self):
        """Test FAQ list with category filter"""
        response = self.client.get(self.list_url, {'category': 1})
        # Should not return error even if no data
        self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND])

    def test_faq_list_with_platform_filter(self):
        """Test FAQ list with platform filter"""
        response = self.client.get(self.list_url, {'platform': 'app'})
        # Should not return error even if no data
        self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND])

    def test_faq_detail_url_pattern(self):
        """Test FAQ detail URL pattern"""
        detail_url = reverse('faq:faq-detail', kwargs={'pk': 1})
        self.assertIn('/api/faq/faq/1/', detail_url)


class FaqUrlPatternsTestCase(TestCase):
    """
    Test cases for FAQ URL patterns
    """

    def test_category_urls(self):
        """Test FAQ category URL patterns"""
        list_url = reverse('faq:faq-category-list')
        detail_url = reverse('faq:faq-category-detail', kwargs={'pk': 1})

        self.assertEqual(list_url, '/api/faq/category/')
        self.assertEqual(detail_url, '/api/faq/category/1/')

    def test_faq_urls(self):
        """Test FAQ URL patterns"""
        list_url = reverse('faq:faq-list')
        detail_url = reverse('faq:faq-detail', kwargs={'pk': 1})

        self.assertEqual(list_url, '/api/faq/faq/')
        self.assertEqual(detail_url, '/api/faq/faq/1/')
