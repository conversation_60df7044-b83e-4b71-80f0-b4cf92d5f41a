from rest_framework import viewsets, status, filters
from rest_framework.permissions import <PERSON>owAny
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Prefetch
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.html import escape
import logging

from utils.pagination import CustomPagination
from utils.response import APIResponse, get_language_from_request
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample

from .models import TcdAppFaqcategory, TcdAppFaq
from .serializers import (
    TcdAppFaqcategorySerializer,
    TcdAppFaqSerializer,
)

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["FAQ Category"]
)
class TcdAppFaqcategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    FAQ Category Master Data API

    This ViewSet provides read-only access to FAQ category master data for mobile applications.
    Features:
    - No authentication required (public access)
    - Performance-optimized queries
    - Mobile-optimized pagination
    - Ordered by order field
    - Multi-language support (Thai/English)

    Supported Operations:
    - GET /api/faq/category/ - List all FAQ categories with pagination
    - GET /api/faq/category/{id}/ - Retrieve specific FAQ category by ID

    Query Parameters:
    - page: Page number for pagination (default: 1)
    - page_size: Items per page (default: 10, max: 100)
    - search: Search in category names (Thai and English)

    Response Format:
    - Consistent APIResponse format with success/error fields
    - Pagination metadata included for list operations
    - Display format: name_th : name_en
    """
    queryset = TcdAppFaqcategory.objects.all().order_by('order', 'id')
    serializer_class = TcdAppFaqcategorySerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name_th', 'name_en']
    ordering_fields = ['order', 'id']
    ordering = ['order', 'id']

    def get_queryset(self):
        """
        Override to add custom filtering and optimization
        """
        queryset = super().get_queryset()

        # Add any additional filtering logic here if needed
        # For example, filter by active status if there was such a field

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        try:
            # Get language preference
            language = get_language_from_request(request)

            # Get queryset and apply filters
            queryset = self.filter_queryset(self.get_queryset())

            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True, context={'request': request})
                paginated_response = self.get_paginated_response(serializer.data)
                return paginated_response

            # If no pagination
            serializer = self.get_serializer(queryset, many=True, context={'request': request})
            return APIResponse.success(
                data={'categories': serializer.data},
                language=language
            )

        except ValidationError as e:
            logger.error(f"Validation error in FAQ category list: {str(e)}")
            return APIResponse.validation_error(str(e), language=get_language_from_request(request))
        except Exception as e:
            logger.error(f"Unexpected error in FAQ category list: {str(e)}")
            return APIResponse.server_error(language=get_language_from_request(request))

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide custom response format
        """
        try:
            # Get language preference
            language = get_language_from_request(request)

            # Get the instance
            instance = self.get_object()
            serializer = self.get_serializer(instance, context={'request': request})

            return APIResponse.success(
                data={'category': serializer.data},
                language=language
            )

        except Exception as e:
            logger.error(f"Error retrieving FAQ category {kwargs.get('pk')}: {str(e)}")
            return APIResponse.not_found(language=get_language_from_request(request))


@extend_schema(
    tags=["FAQ"]
)
class TcdAppFaqViewSet(viewsets.ReadOnlyModelViewSet):
    """
    FAQ API for Mobile Applications

    This ViewSet provides read-only access to FAQ data for mobile applications.
    Features:
    - No authentication required (public access)
    - Category-based filtering
    - Platform-specific filtering (app/web)
    - Performance-optimized queries with category prefetch
    - Mobile-optimized pagination
    - Multi-language support (Thai/English)
    - Search functionality across questions and answers

    Supported Operations:
    - GET /api/faq/faq/ - List all FAQs with pagination and filtering
    - GET /api/faq/faq/{id}/ - Retrieve specific FAQ by ID
    - GET /api/faq/faq/by_category/{category_id}/ - List FAQs by category

    Query Parameters:
    - page: Page number for pagination (default: 1)
    - page_size: Items per page (default: 10, max: 100)
    - category: Filter by category ID
    - platform: Filter by platform ('app', 'web', or 'both')
    - search: Search in questions and answers (Thai and English)
    - status: Filter by status (default: active only)

    Response Format:
    - Consistent APIResponse format with success/error fields
    - Pagination metadata included for list operations
    - Language-specific content based on user preference
    """
    queryset = TcdAppFaq.objects.all().order_by('-create_date', 'id')
    serializer_class = TcdAppFaqSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['question_th', 'question_en', 'answer_th', 'answer_en']
    ordering_fields = ['id', 'create_date', 'update_date']
    ordering = ['-create_date', 'id']

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        try:
            queryset = TcdAppFaq.objects.filter(status=True).order_by('-create_date', 'id')
            # Get language preference
            language = get_language_from_request(request)

            # Get queryset and apply filters
            queryset = self.filter_queryset(self.get_queryset())

            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True, context={'request': request})
                paginated_response = self.get_paginated_response(serializer.data)
                return paginated_response

            # If no pagination
            serializer = self.get_serializer(queryset, many=True, context={'request': request})
            return APIResponse.success(
                data={'faqs': serializer.data},
                language=language
            )

        except ValidationError as e:
            logger.error(f"Validation error in FAQ list: {str(e)}")
            return APIResponse.validation_error(str(e), language=get_language_from_request(request))
        except Exception as e:
            logger.error(f"Unexpected error in FAQ list: {str(e)}")
            return APIResponse.server_error(language=get_language_from_request(request))

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to provide custom response format
        """
        try:
            # Get language preference
            language = get_language_from_request(request)

            # Get the instance
            instance = self.get_object()
            serializer = self.get_serializer(instance, context={'request': request})

            return APIResponse.success(
                data={'faq': serializer.data},
                language=language
            )

        except Exception as e:
            logger.error(f"Error retrieving FAQ {kwargs.get('pk')}: {str(e)}")
            return APIResponse.not_found(language=get_language_from_request(request))

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name='category_id',
                description='FAQ Category ID',
                required=True,
                type=int,
                location=OpenApiParameter.PATH
            ),
            OpenApiParameter(
                name='page',
                description='Page number',
                required=False,
                type=int,
                location=OpenApiParameter.QUERY
            ),
            OpenApiParameter(
                name='page_size',
                description='Items per page (max: 100)',
                required=False,
                type=int,
                location=OpenApiParameter.QUERY
            ),
            OpenApiParameter(
                name='platform',
                description='Filter by platform',
                required=False,
                type=str,
                enum=['app', 'web', 'both'],
                location=OpenApiParameter.QUERY
            ),
        ]
    )
    @action(detail=False, methods=['get'], url_path='by_category/(?P<category_id>[^/.]+)')
    def by_category(self, request, category_id=None):
        """
        Get FAQs by category ID

        This endpoint provides FAQs filtered by a specific category.
        Supports all the same filtering and pagination options as the main list endpoint.
        """
        try:
            # Get language preference
            language = get_language_from_request(request)

            # Validate category_id
            try:
                category_id = int(category_id)
            except (ValueError, TypeError):
                return APIResponse.validation_error(
                    "Invalid category ID provided",
                    language=language
                )

            # Check if category exists
            if not TcdAppFaqcategory.objects.filter(id=category_id).exists():
                return APIResponse.not_found(
                    error_code=3002,  # Data not found
                    language=language
                )

            # Get queryset filtered by category
            queryset = self.get_queryset().filter(app_faqcategory_id=category_id)
            queryset = self.filter_queryset(queryset)

            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True, context={'request': request})
                paginated_response = self.get_paginated_response(serializer.data)
                return paginated_response

            # If no pagination
            serializer = self.get_serializer(queryset, many=True, context={'request': request})
            return APIResponse.success(
                data={
                    'category_id': category_id,
                    'faqs': serializer.data
                },
                language=language
            )

        except ValidationError as e:
            logger.error(f"Validation error in FAQ by_category: {str(e)}")
            return APIResponse.validation_error(str(e), language=get_language_from_request(request))
        except Exception as e:
            logger.error(f"Unexpected error in FAQ by_category: {str(e)}")
            return APIResponse.server_error(language=get_language_from_request(request))
